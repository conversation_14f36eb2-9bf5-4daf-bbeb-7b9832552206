{"version": "0.2", "language": "en,nl", "words": ["vloerwerk", "VLOERWERK", "applicatie", "regels", "kunnen", "een<PERSON><PERSON>g", "aangepast", "worden", "zonder", "wijzigingen", "Materiaal", "MATERIAAL", "PRIJZEN", "materiaal", "arbeid", "<PERSON><PERSON><PERSON><PERSON>", "prijs", "complexiteit", "BTW", "berekening", "bedrag", "oppervlakte", "lengte", "breedte", "hoogte", "afmetingen", "validatie", "fotos", "upload", "bestandsgrootte", "bestandstype", "bestandsformaat", "toegestane", "formaten", "jpeg", "jpg", "png", "webp", "bestandsnaam", "timestamp", "filepath", "arrayBuffer", "buffer", "formData", "multipart", "insensitive", "contains", "orderBy", "take", "skip", "aggregate", "groupBy", "count", "find<PERSON>any", "findUnique", "create", "update", "delete", "deleteMany", "where", "include", "data", "fields", "references", "cuid", "default", "now", "updatedAt", "createdAt", "projectId", "filename", "url", "condition", "priceEstimate", "confidence", "material", "measurement", "measurements", "analysis", "analyses", "photo", "photos", "project", "projects", "clientName", "clientEmail", "status", "<PERSON><PERSON>w", "actief", "voltooid", "gefactureerd", "concept", "verzonden", "<PERSON><PERSON>", "vervallen", "open", "bezig", "klant", "klanten", "klantId", "klantgegevens", "naam", "email", "telefoon", "adres", "postcode", "plaats", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "afmetingen", "oppervlakte", "aiAnalyse", "geschatteP<PERSON><PERSON>s", "factuur", "facturen", "factuurId", "nummer", "datum", "vervaldatum", "subtotaal", "btw", "totaal", "regels", "regel", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "aantal", "eenheidsprijs", "taak", "taken", "taakId", "titel", "prioriteit", "deadline", "device", "mobile", "desktop", "tablet", "userAgent", "screenWidth", "isMobile", "isTablet", "isDesktop", "deviceType", "detectDevice", "getDeviceType", "formatCurrency", "formatDate", "formatDateTime", "validateEmail", "validatePhone", "validatePostcode", "getFileExtension", "formatFileSize", "isValidImageFile", "capitalizeFirst", "truncateText", "slugify", "chunkArray", "uniqueArray", "sortByProperty", "addDays", "isToday", "isYesterday", "getDaysBetween", "roundToDecimals", "calculatePercentage", "calculateBTW", "calculateBTWInclusive", "getStatusColor", "getPriorityColor", "handleError", "isNetworkError", "saveToLocalStorage", "getFromLocalStorage", "removeFromLocalStorage", "debounce", "throttle", "cn", "clsx", "twMerge", "ClassValue", "DeviceInfo", "PrijsCalculatie", "VisionAnalysisResult", "AIAnalyseResult", "MateriaalType", "OppervlakteType", "ProjectStatus", "FactuurStatus", "TaakStatus", "AfmetingenInput", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "VloerProject", "ProjectFoto", "<PERSON><PERSON><PERSON><PERSON>", "FactuurRegel", "<PERSON><PERSON>", "ProjectFormData", "CameraConfig", "ApiResponse", "PaginatedResponse", "laminaat", "parket", "tegels", "vinyl", "tapi<PERSON><PERSON>", "na<PERSON><PERSON><PERSON>", "glad", "ruw", "gestructureerd", "besch<PERSON>g<PERSON>", "ongelijk", "vochtig", "woon<PERSON><PERSON>", "keuken", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "kanto<PERSON>", "winkel", "klein", "middel", "groot", "uitstekend", "goed", "redelijk", "slecht", "vervangen", "een<PERSON><PERSON>g", "norm<PERSON>", "complex", "zeer", "voorbereidingsTijd", "afwerkingsTijd", "minimumTijd", "basisTijd", "conditieScore", "complexiteitscore", "oppervlakteType", "aanbevolenMethode", "geschatteTijd", "geschatteKosten", "rawData", "kleurToon", "textuur", "beschadigingen", "oppervlakteKwaliteit", "minimumOppervlakte", "maximumOppervlakte", "standaardHoogte", "garantiePeriode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "a<PERSON><PERSON><PERSON>", "minLengte", "maxLengte", "minBreedte", "max<PERSON><PERSON><PERSON>", "maxAantal", "max<PERSON><PERSON><PERSON>", "minimumConfidence", "maxAnalyseTijd", "<PERSON><PERSON><PERSON><PERSON><PERSON>lePri<PERSON><PERSON>", "berekenGeschatteTijd", "bepaalProjectGrootte", "valideerAfmetingen", "berekenOppervlakte", "ProjectService", "PhotoService", "MeasurementService", "AnalysisService", "StatisticsService", "DatabaseUtils", "AIService", "aiService", "BusinessRules", "MATERIAAL_PRIJZEN", "BTW_REGELS", "COMPLEXITEIT_FACTOREN", "TIJD_SCHATTINGEN", "KWALITEIT_SCORES", "PROJECT_REGELS", "VALIDATIE_REGELS", "UPLOAD_CONFIG", "AI_CONFIG", "ANALYSIS_PROMPT", "CameraCapture", "DeviceDetection", "Error<PERSON>ou<PERSON><PERSON>", "MobileProjectPage", "DesktopDashboard", "HomePage", "RootLayout", "DeviceDetectionProps", "CameraCaptureProps", "CameraState", "ProjectState", "Stats", "Project", "DeviceDetection", "DeviceInfo", "PrijsCalculatie", "VisionAnalysisResult", "AIAnalyseResult", "MateriaalType", "OppervlakteType", "ProjectStatus", "FactuurStatus", "TaakStatus", "AfmetingenInput", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "VloerProject", "ProjectFoto", "<PERSON><PERSON><PERSON><PERSON>", "FactuurRegel", "<PERSON><PERSON>", "ProjectFormData", "CameraConfig", "ApiResponse", "PaginatedResponse"], "ignorePaths": ["node_modules/**", "dist/**", "build/**", ".next/**", "coverage/**", "*.log", "package-lock.json", "pnpm-lock.yaml"]}
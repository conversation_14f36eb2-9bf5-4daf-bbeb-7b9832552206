// src/hooks/useCamera.ts
/**
 * 📸 USE CAMERA HOOK
 * 
 * Custom React hook voor camera functionaliteit.
 * Gebruikt de camera service voor photo capture en management.
 */

import { useState, useEffect, useCallback } from 'react';
import { cameraService, type CameraState, type PhotoCapture, type CameraConfig } from '@/lib/mobile/camera';

export function useCamera() {
  const [state, setState] = useState<CameraState>({
    isActive: false,
    isCapturing: false,
    hasPermission: false,
    error: null,
    stream: null,
  });

  const [isAvailable, setIsAvailable] = useState<boolean>(false);
  const [lastPhoto, setLastPhoto] = useState<PhotoCapture | null>(null);

  // Check camera availability
  useEffect(() => {
    const checkAvailability = async () => {
      const available = await cameraService.isAvailable();
      setIsAvailable(available);
    };

    checkAvailability();
  }, []);

  // Subscribe to camera state changes
  useEffect(() => {
    const updateState = () => {
      setState(cameraService.getState());
    };

    // Poll state changes (in real app, use event emitter)
    const interval = setInterval(updateState, 100);

    return () => clearInterval(interval);
  }, []);

  /**
   * Start camera
   */
  const startCamera = useCallback(async (config?: Partial<CameraConfig>): Promise<boolean> => {
    if (config) {
      cameraService.configure(config);
    }

    const success = await cameraService.startCamera();
    if (success) {
      setState(cameraService.getState());
    }
    return success;
  }, []);

  /**
   * Stop camera
   */
  const stopCamera = useCallback(() => {
    cameraService.stopCamera();
    setState(cameraService.getState());
  }, []);

  /**
   * Capture photo
   */
  const capturePhoto = useCallback(async (): Promise<PhotoCapture | null> => {
    try {
      const photo = await cameraService.capturePhoto();
      if (photo) {
        setLastPhoto(photo);
      }
      setState(cameraService.getState());
      return photo;
    } catch (error) {
      console.error('Photo capture failed:', error);
      return null;
    }
  }, []);

  /**
   * Switch camera (front/back)
   */
  const switchCamera = useCallback(async (): Promise<boolean> => {
    const success = await cameraService.switchCamera();
    setState(cameraService.getState());
    return success;
  }, []);

  /**
   * Toggle flash
   */
  const toggleFlash = useCallback(() => {
    cameraService.toggleFlash();
  }, []);

  /**
   * Configure camera
   */
  const configure = useCallback((config: Partial<CameraConfig>) => {
    cameraService.configure(config);
  }, []);

  /**
   * Get camera devices
   */
  const getDevices = useCallback(async () => {
    return await cameraService.getDevices();
  }, []);

  /**
   * Clear error
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  /**
   * Cleanup on unmount
   */
  useEffect(() => {
    return () => {
      cameraService.destroy();
    };
  }, []);

  return {
    // State
    state,
    isAvailable,
    lastPhoto,

    // Actions
    startCamera,
    stopCamera,
    capturePhoto,
    switchCamera,
    toggleFlash,
    configure,
    getDevices,
    clearError,

    // Computed
    isActive: state.isActive,
    isCapturing: state.isCapturing,
    hasPermission: state.hasPermission,
    error: state.error,
    stream: state.stream,
  };
}

// Export hook
export default useCamera; 
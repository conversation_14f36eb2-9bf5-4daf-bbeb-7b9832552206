// src/hooks/useAuth.ts
/**
 * 🔐 USE AUTH HOOK
 * 
 * Custom React hook voor authenticatie state management.
 * Gebruikt de auth service voor login/logout en user management.
 */

import { useState, useEffect } from 'react';
import { authService, type AuthState, type LoginCredentials, type RegisterData, type User } from '@/lib/auth/auth-service';

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  useEffect(() => {
    // Subscribe to auth state changes
    const unsubscribe = authService.subscribe(setAuthState);

    // Check initial auth state
    authService.checkAuth();

    return unsubscribe;
  }, []);

  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    return await authService.login(credentials);
  };

  const register = async (data: RegisterData): Promise<boolean> => {
    return await authService.register(data);
  };

  const logout = async (): Promise<void> => {
    await authService.logout();
  };

  const updateProfile = async (data: Partial<User>): Promise<boolean> => {
    return await authService.updateProfile(data);
  };

  const hasPermission = (permission: string): boolean => {
    return authService.hasPermission(permission);
  };

  const clearError = (): void => {
    authService.clearError();
  };

  return {
    // State
    user: authState.user,
    isAuthenticated: authState.isAuthenticated,
    isLoading: authState.isLoading,
    error: authState.error,

    // Actions
    login,
    register,
    logout,
    updateProfile,
    hasPermission,
    clearError,
  };
}

// Export hook
export default useAuth; 
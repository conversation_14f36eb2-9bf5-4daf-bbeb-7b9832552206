// src/lib/business-rules.ts
/**
 * 🏗️ VLOERWERK CRM - BUSINESS RULES
 * 
 * Alle business logic en regels voor de vloerwerk CRM applicatie.
 * Deze regels kunnen eenvoudig aangepast worden zonder code wijzigingen.
 */

import { MateriaalType } from '@/types';

// ===== MATERIAAL PRIJZEN =====
export const MATERIAAL_PRIJZEN = {
  [MateriaalType.LAMINAAT]: {
    materiaal: 15,      // €15 per m²
    arbeid: 8,          // €8 per m²
    minimumPrijs: 150,  // Minimum project prijs
    complexiteitFactor: 1.0
  },
  [MateriaalType.PARKET]: {
    materiaal: 35,      // €35 per m²
    arbeid: 12,         // €12 per m²
    minimumPrijs: 300,
    complexiteitFactor: 1.2
  },
  [MateriaalType.TEGELS]: {
    materiaal: 25,      // €25 per m²
    arbeid: 15,         // €15 per m²
    minimumPrijs: 200,
    complexiteitFactor: 1.3
  },
  [MateriaalType.VINYL]: {
    materiaal: 20,      // €20 per m²
    arbeid: 10,         // €10 per m²
    minimumPrijs: 180,
    complexiteitFactor: 0.9
  },
  [MateriaalType.NATUURSTEEN]: {
    materiaal: 80,      // €80 per m²
    arbeid: 25,         // €25 per m²
    minimumPrijs: 500,
    complexiteitFactor: 1.5
  }
} as const;

// ===== BTW EN BELASTINGEN =====
export const BTW_REGELS = {
  percentage: 21,                                    // 21% BTW
  berekening: (bedrag: number) => bedrag * 1.21,   // Inclusief BTW
  btwBedrag: (bedrag: number) => bedrag * 0.21     // BTW bedrag
} as const;

// ===== COMPLEXITEIT FACTOREN =====
export const COMPLEXITEIT_FACTOREN = {
  oppervlakteType: {
    'glad': 1.0,           // Normale vloer
    'ruw': 1.2,            // Ruwe ondergrond
    'beschadigd': 1.5,     // Beschadigde vloer
    'ongelijk': 1.3,       // Ongelijke vloer
    'vochtig': 1.4         // Vochtige ruimte
  },
  ruimteType: {
    'woonkamer': 1.0,      // Standaard ruimte
    'keuken': 1.2,         // Extra voorzichtigheid
    'badkamer': 1.5,       // Waterbestendig
    'slaapkamer': 0.9,     // Eenvoudiger
    'kantoor': 1.1,        // Professioneel
    'winkel': 1.3          // Zwaar gebruik
  },
  projectGrootte: {
    klein: 1.2,            // < 20m² - meer werk per m²
    middel: 1.0,           // 20-50m² - standaard
    groot: 0.9             // > 50m² - schaalvoordeel
  }
} as const;

// ===== TIJD SCHATTINGEN =====
export const TIJD_SCHATTINGEN = {
  basisTijd: {
    [MateriaalType.LAMINAAT]: 0.5,      // 30 min per m²
    [MateriaalType.PARKET]: 0.8,        // 48 min per m²
    [MateriaalType.TEGELS]: 1.0,        // 60 min per m²
    [MateriaalType.VINYL]: 0.4,         // 24 min per m²
    [MateriaalType.NATUURSTEEN]: 1.5    // 90 min per m²
  },
  voorbereidingsTijd: 2,                 // 2 uur voorbereiding
  afwerkingsTijd: 1,                     // 1 uur afwerking
  minimumTijd: 4                         // Minimum 4 uur per project
} as const;

// ===== KWALITEIT SCORES =====
export const KWALITEIT_SCORES = {
  conditieScore: {
    uitstekend: 9,         // Perfecte staat
    goed: 7,               // Kleine gebreken
    redelijk: 5,           // Zichtbare slijtage
    slecht: 3,             // Veel schade
    vervangen: 1           // Moet vervangen
  },
  complexiteitscore: {
    eenvoudig: 2,          // Rechte lijnen, geen obstakels
    normaal: 5,            // Standaard project
    complex: 8,            // Veel hoeken, obstakels
    zeer_complex: 10       // Zeer moeilijk project
  }
} as const;

// ===== PROJECT REGELS =====
export const PROJECT_REGELS = {
  minimumOppervlakte: 5,                 // Minimum 5m²
  maximumOppervlakte: 500,               // Maximum 500m²
  standaardHoogte: 2.5,                  // 2.5m standaard hoogte
  garantiePeriode: 24,                   // 24 maanden garantie
  betalingsTermijn: 30,                  // 30 dagen betaaltermijn
  aanbetaling: 0.3                       // 30% aanbetaling
} as const;

// ===== VALIDATIE REGELS =====
export const VALIDATIE_REGELS = {
  afmetingen: {
    minLengte: 1,          // Minimum 1 meter
    maxLengte: 50,         // Maximum 50 meter
    minBreedte: 1,         // Minimum 1 meter
    maxBreedte: 50         // Maximum 50 meter
  },
  fotos: {
    maxAantal: 10,         // Maximum 10 foto's
    maxGrootte: 10,        // 10MB per foto
    toegestaneFormaten: ['jpeg', 'jpg', 'png', 'webp']
  },
  ai: {
    minimumConfidence: 0.7,  // 70% minimum betrouwbaarheid
    maxAnalyseTijd: 30       // 30 seconden max analyse tijd
  }
} as const;

// ===== HELPER FUNCTIES =====
export const BusinessRules = {
  /**
   * Bereken totale prijs inclusief BTW
   */
  berekenTotalePrijs(materiaalType: MateriaalType, oppervlakte: number, complexiteitFactor = 1): number {
    const prijzen = MATERIAAL_PRIJZEN[materiaalType];
    const materiaalKosten = oppervlakte * prijzen.materiaal;
    const arbeidsKosten = oppervlakte * prijzen.arbeid * complexiteitFactor;
    const subtotaal = Math.max(materiaalKosten + arbeidsKosten, prijzen.minimumPrijs);
    
    return BTW_REGELS.berekening(subtotaal);
  },

  /**
   * Bereken geschatte tijd
   */
  berekenGeschatteTijd(materiaalType: MateriaalType, oppervlakte: number, complexiteitFactor = 1): number {
    const basisTijd = TIJD_SCHATTINGEN.basisTijd[materiaalType] * oppervlakte;
    const totaleTijd = basisTijd * complexiteitFactor + 
                      TIJD_SCHATTINGEN.voorbereidingsTijd + 
                      TIJD_SCHATTINGEN.afwerkingsTijd;
    
    return Math.max(totaleTijd, TIJD_SCHATTINGEN.minimumTijd);
  },

  /**
   * Bepaal project grootte categorie
   */
  bepaalProjectGrootte(oppervlakte: number): 'klein' | 'middel' | 'groot' {
    if (oppervlakte < 20) return 'klein';
    if (oppervlakte <= 50) return 'middel';
    return 'groot';
  },

  /**
   * Valideer project afmetingen
   */
  valideerAfmetingen(lengte: number, breedte: number): boolean {
    const { minLengte, maxLengte, minBreedte, maxBreedte } = VALIDATIE_REGELS.afmetingen;
    return lengte >= minLengte && lengte <= maxLengte && 
           breedte >= minBreedte && breedte <= maxBreedte;
  },

  /**
   * Bereken oppervlakte met precisie
   */
  berekenOppervlakte(lengte: number, breedte: number): number {
    return Math.round((lengte * breedte + Number.EPSILON) * 100) / 100;
  }
};

// src/lib/validations.ts
import { z } from 'zod';

// Klant validatie
export const klantSchema = z.object({
  naam: z.string().min(1, "Naam is verplicht").max(100, "Naam is te lang"),
  email: z.string().email("Ongeldig email adres").optional(),
  telefoon: z.string().min(10, "Telefoonnummer is te kort").optional(),
  adres: z.string().max(200, "Adres is te lang").optional(),
  postcode: z.string().max(10, "Postcode is te lang").optional(),
  plaats: z.string().max(100, "Plaats is te lang").optional()
});

// Project validatie
export const projectSchema = z.object({
  naam: z.string().min(1, "Project naam is verplicht").max(100, "Project naam is te lang"),
  beschrijving: z.string().max(500, "Beschrijving is te lang").optional(),
  klantId: z.string().min(1, "Klant is verplicht"),
  lengte: z.number().positive("Lengte moet positief zijn").max(1000, "Lengte is te groot").optional(),
  breedte: z.number().positive("Breedte moet positief zijn").max(1000, "Breedte is te groot").optional(),
  hoogte: z.number().positive("Hoogte moet positief zijn").max(100, "Hoogte is te groot").optional()
});

// Afmetingen validatie
export const afmetingenSchema = z.object({
  lengte: z.number().positive("Lengte moet positief zijn").max(1000, "Lengte is te groot"),
  breedte: z.number().positive("Breedte moet positief zijn").max(1000, "Breedte is te groot"),
  hoogte: z.number().positive("Hoogte moet positief zijn").max(100, "Hoogte is te groot").optional()
});

// Foto upload validatie
export const fotoUploadSchema = z.object({
  file: z.instanceof(File, "Bestand is verplicht"),
  description: z.string().max(200, "Beschrijving is te lang").optional()
});

// Factuur validatie
export const factuurSchema = z.object({
  projectId: z.string().min(1, "Project is verplicht"),
  klantId: z.string().min(1, "Klant is verplicht"),
  subtotaal: z.number().positive("Subtotaal moet positief zijn"),
  btw: z.number().positive("BTW moet positief zijn"),
  totaal: z.number().positive("Totaal moet positief zijn"),
  vervaldatum: z.date().min(new Date(), "Vervaldatum moet in de toekomst liggen")
});

// Taak validatie
export const taakSchema = z.object({
  titel: z.string().min(1, "Titel is verplicht").max(100, "Titel is te lang"),
  beschrijving: z.string().max(500, "Beschrijving is te lang").optional(),
  projectId: z.string().min(1, "Project is verplicht"),
  prioriteit: z.number().min(1, "Prioriteit moet minimaal 1 zijn").max(5, "Prioriteit moet maximaal 5 zijn"),
  deadline: z.date().optional()
});

// Search validatie
export const searchSchema = z.object({
  query: z.string().min(1, "Zoekterm is verplicht").max(100, "Zoekterm is te lang"),
  filters: z.object({
    status: z.string().optional(),
    materiaalType: z.string().optional(),
    dateRange: z.object({
      start: z.date().optional(),
      end: z.date().optional()
    }).optional()
  }).optional()
});

// Pagination validatie
export const paginationSchema = z.object({
  page: z.number().min(1, "Pagina moet minimaal 1 zijn"),
  limit: z.number().min(1, "Limit moet minimaal 1 zijn").max(100, "Limit moet maximaal 100 zijn")
});

// Export types
export type KlantInput = z.infer<typeof klantSchema>;
export type ProjectInput = z.infer<typeof projectSchema>;
export type AfmetingenInput = z.infer<typeof afmetingenSchema>;
export type FotoUploadInput = z.infer<typeof fotoUploadSchema>;
export type FactuurInput = z.infer<typeof factuurSchema>;
export type TaakInput = z.infer<typeof taakSchema>;
export type SearchInput = z.infer<typeof searchSchema>;
export type PaginationInput = z.infer<typeof paginationSchema>;

// src/lib/mobile/camera.ts
/**
 * 📸 CAMERA SERVICE
 * 
 * Camera functionaliteit voor mobile PWA.
 * Handelt camera access, photo capture en upload.
 */

import { CameraConfig, CameraState, PhotoCapture, PhotoMetadata, MobileError, MobileErrorCode } from '@/types/mobile';

class CameraService {
  private static instance: CameraService;
  private config: CameraConfig;
  private state: CameraState;
  private stream: MediaStream | null = null;
  private videoElement: HTMLVideoElement | null = null;
  private canvasElement: HTMLCanvasElement | null = null;

  constructor() {
    this.config = {
      quality: 0.8,
      maxWidth: 1920,
      maxHeight: 1080,
      format: 'jpeg',
      flash: 'auto',
      facingMode: 'environment',
    };

    this.state = {
      isActive: false,
      isCapturing: false,
      hasPermission: false,
      error: null,
      stream: null,
    };
  }

  static getInstance(): CameraService {
    if (!CameraService.instance) {
      CameraService.instance = new CameraService();
    }
    return CameraService.instance;
  }

  /**
   * Configureer camera instellingen
   */
  configure(config: Partial<CameraConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * Vraag camera toestemming en start camera
   */
  async startCamera(): Promise<boolean> {
    try {
      this.state.isActive = true;
      this.state.error = null;

      // Vraag camera toestemming
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: this.config.facingMode,
          width: { ideal: this.config.maxWidth },
          height: { ideal: this.config.maxHeight },
        },
        audio: false,
      });

      this.stream = stream;
      this.state.stream = stream;
      this.state.hasPermission = true;

      return true;
    } catch (error) {
      this.state.error = this.handleCameraError(error);
      this.state.hasPermission = false;
      return false;
    }
  }

  /**
   * Stop camera
   */
  stopCamera(): void {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }

    this.state.isActive = false;
    this.state.stream = null;
    this.state.error = null;
  }

  /**
   * Maak foto
   */
  async capturePhoto(): Promise<PhotoCapture | null> {
    if (!this.stream) {
      throw new Error('Camera niet actief');
    }

    try {
      this.state.isCapturing = true;

      // Maak canvas voor foto capture
      const canvas = document.createElement('canvas');
      const video = document.createElement('video');
      
      // Stel video source in
      video.srcObject = this.stream;
      await video.play();

      // Stel canvas afmetingen in
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Teken video frame naar canvas
      const ctx = canvas.getContext('2d');
      if (!ctx) throw new Error('Canvas context niet beschikbaar');
      
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Converteer naar blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob(resolve, `image/${this.config.format}`, this.config.quality);
      });

      // Maak metadata
      const metadata: PhotoMetadata = {
        width: canvas.width,
        height: canvas.height,
        format: this.config.format,
        size: blob.size,
        timestamp: new Date(),
      };

      // Voeg locatie toe als beschikbaar
      if (navigator.geolocation) {
        try {
          const position = await this.getCurrentPosition();
          metadata.location = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
          };
        } catch (error) {
          console.warn('Locatie niet beschikbaar:', error);
        }
      }

      // Genereer filename
      const filename = `photo_${Date.now()}.${this.config.format}`;

      const photoCapture: PhotoCapture = {
        blob,
        filename,
        metadata,
      };

      // Comprimeer foto als nodig
      if (blob.size > 5 * 1024 * 1024) { // 5MB
        photoCapture.compressed = await this.compressPhoto(blob);
      }

      this.state.isCapturing = false;
      return photoCapture;

    } catch (error) {
      this.state.isCapturing = false;
      this.state.error = this.handleCameraError(error);
      return null;
    }
  }

  /**
   * Comprimeer foto
   */
  private async compressPhoto(blob: Blob): Promise<Blob> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Bereken nieuwe afmetingen
        const maxSize = 1024;
        let { width, height } = img;

        if (width > height) {
          if (width > maxSize) {
            height = (height * maxSize) / width;
            width = maxSize;
          }
        } else {
          if (height > maxSize) {
            width = (width * maxSize) / height;
            height = maxSize;
          }
        }

        canvas.width = width;
        canvas.height = height;

        if (ctx) {
          ctx.drawImage(img, 0, 0, width, height);
          canvas.toBlob(resolve, `image/${this.config.format}`, 0.7);
        }
      };

      img.src = URL.createObjectURL(blob);
    });
  }

  /**
   * Haal huidige locatie op
   */
  private getCurrentPosition(): Promise<GeolocationPosition> {
    return new Promise((resolve, reject) => {
      navigator.geolocation.getCurrentPosition(resolve, reject, {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      });
    });
  }

  /**
   * Handle camera errors
   */
  private handleCameraError(error: any): string {
    console.error('Camera error:', error);

    if (error.name === 'NotAllowedError') {
      return 'Camera toestemming geweigerd';
    } else if (error.name === 'NotFoundError') {
      return 'Camera niet gevonden';
    } else if (error.name === 'NotSupportedError') {
      return 'Camera niet ondersteund';
    } else if (error.name === 'NotReadableError') {
      return 'Camera niet beschikbaar';
    } else if (error.name === 'OverconstrainedError') {
      return 'Camera configuratie niet ondersteund';
    } else {
      return 'Onbekende camera fout';
    }
  }

  /**
   * Wissel camera (front/back)
   */
  async switchCamera(): Promise<boolean> {
    if (!this.stream) return false;

    // Stop huidige camera
    this.stopCamera();

    // Wissel facing mode
    this.config.facingMode = this.config.facingMode === 'user' ? 'environment' : 'user';

    // Start nieuwe camera
    return await this.startCamera();
  }

  /**
   * Toggle flash
   */
  toggleFlash(): void {
    this.config.flash = this.config.flash === 'on' ? 'off' : 'on';
  }

  /**
   * Get camera state
   */
  getState(): CameraState {
    return { ...this.state };
  }

  /**
   * Check of camera beschikbaar is
   */
  async isAvailable(): Promise<boolean> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.some(device => device.kind === 'videoinput');
    } catch (error) {
      return false;
    }
  }

  /**
   * Get camera devices
   */
  async getDevices(): Promise<MediaDeviceInfo[]> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      return devices.filter(device => device.kind === 'videoinput');
    } catch (error) {
      return [];
    }
  }

  /**
   * Cleanup resources
   */
  destroy(): void {
    this.stopCamera();
    this.state = {
      isActive: false,
      isCapturing: false,
      hasPermission: false,
      error: null,
      stream: null,
    };
  }
}

// Export singleton instance
export const cameraService = CameraService.getInstance();

// Export types
export type { CameraConfig, CameraState, PhotoCapture, PhotoMetadata }; 
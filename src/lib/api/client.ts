// src/lib/api/client.ts
/**
 * 🌐 API CLIENT
 * 
 * Centrale API client voor frontend-backend communicatie.
 * Handelt alle HTTP requests af met error handling en type safety.
 */

import { ApiResponse, PaginatedResponse } from '@/types';

class ApiClient {
  private baseUrl: string;
  private defaultHeaders: HeadersInit;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
    this.defaultHeaders = {
      'Content-Type': 'application/json',
    };
  }

  /**
   * Voeg auth token toe aan headers
   */
  private getAuthHeaders(): HeadersInit {
    const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
    return {
      ...this.defaultHeaders,
      ...(token && { Authorization: `Bearer ${token}` }),
    };
  }

  /**
   * Generic GET request
   */
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<ApiResponse<T>> {
    const url = new URL(`${this.baseUrl}${endpoint}`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, String(value));
      });
    }

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  /**
   * Generic POST request
   */
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: data ? JSON.stringify(data) : undefined,
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  /**
   * Generic PUT request
   */
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: data ? JSON.stringify(data) : undefined,
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  /**
   * Generic DELETE request
   */
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  /**
   * File upload met FormData
   */
  async upload<T>(endpoint: string, file: File, additionalData?: Record<string, any>): Promise<ApiResponse<T>> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
    }

    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: {
          ...this.getAuthHeaders(),
          // Verwijder Content-Type voor FormData
        },
        body: formData,
      });

      return await this.handleResponse<T>(response);
    } catch (error) {
      return this.handleError<T>(error);
    }
  }

  /**
   * Handle API response
   */
  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      return {
        success: false,
        error: errorData.message || `HTTP ${response.status}: ${response.statusText}`,
        data: undefined,
      };
    }

    try {
      const data = await response.json();
      return {
        success: true,
        data,
        error: undefined,
      };
    } catch (error) {
      return {
        success: false,
        error: 'Invalid JSON response',
        data: undefined,
      };
    }
  }

  /**
   * Handle network errors
   */
  private handleError<T>(error: any): ApiResponse<T> {
    console.error('API Error:', error);
    return {
      success: false,
      error: error.message || 'Network error',
      data: undefined,
    };
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Convenience methods voor specifieke endpoints
export const api = {
  // Auth endpoints
  auth: {
    login: (credentials: { email: string; password: string }) =>
      apiClient.post('/auth/login', credentials),
    register: (userData: { email: string; password: string; name: string }) =>
      apiClient.post('/auth/register', userData),
    logout: () => apiClient.post('/auth/logout'),
  },

  // Project endpoints
  projects: {
    getAll: (params?: { page?: number; limit?: number; status?: string }) =>
      apiClient.get('/projects', params),
    getById: (id: string) => apiClient.get(`/projects/${id}`),
    create: (data: any) => apiClient.post('/projects', data),
    update: (id: string, data: any) => apiClient.put(`/projects/${id}`, data),
    delete: (id: string) => apiClient.delete(`/projects/${id}`),
  },

  // Photo endpoints
  photos: {
    upload: (file: File, projectId: string) =>
      apiClient.upload('/photos/upload', file, { projectId }),
    getByProject: (projectId: string) =>
      apiClient.get(`/photos/project/${projectId}`),
    delete: (id: string) => apiClient.delete(`/photos/${id}`),
  },

  // AI endpoints
  ai: {
    analyze: (photoUrl: string, projectId: string) =>
      apiClient.post('/ai/analyze', { photoUrl, projectId }),
    getModels: () => apiClient.get('/ai/models'),
  },

  // Analytics endpoints
  analytics: {
    getStats: () => apiClient.get('/analytics/stats'),
    getReports: (params?: { startDate?: string; endDate?: string }) =>
      apiClient.get('/analytics/reports', params),
  },

  // User endpoints
  users: {
    getProfile: () => apiClient.get('/users/profile'),
    updateProfile: (data: any) => apiClient.put('/users/profile', data),
    getAll: (params?: { page?: number; limit?: number }) =>
      apiClient.get('/users', params),
  },
}; 
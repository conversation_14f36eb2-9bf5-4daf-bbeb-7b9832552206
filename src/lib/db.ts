// src/lib/db.ts
/**
 * 🗄️ DATABASE SERVICE
 * 
 * Alle database operaties voor de vloerwerk CRM applicatie.
 * Gebruikt Prisma voor type-safe database queries.
 */

import { PrismaClient } from '@prisma/client';
import { ProjectStatus, MateriaalType } from '@/types';
import { BusinessRules } from './business-rules';

const globalForPrisma = globalThis as unknown as {
  prisma: PrismaClient | undefined;
};

export const prisma = globalForPrisma.prisma ?? new PrismaClient();

if (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;

// ===== PROJECT OPERATIES =====
export const ProjectService = {
  /**
   * Maak nieuw project aan
   */
  async createProject(data: {
    name: string;
    clientName: string;
    clientEmail?: string;
    status?: ProjectStatus;
  }) {
    return await prisma.project.create({
      data: {
        name: data.name,
        clientName: data.clientName,
        clientEmail: data.clientEmail,
        status: data.status || 'NIEUW'
      }
    });
  },

  /**
   * Haal project op met alle gerelateerde data
   */
  async getProject(id: string) {
    return await prisma.project.findUnique({
      where: { id },
      include: {
        photos: {
          orderBy: { createdAt: 'desc' }
        },
        measurements: true,
        analysis: {
          orderBy: { createdAt: 'desc' },
          take: 1
        }
      }
    });
  },

  /**
   * Haal alle projecten op met filtering
   */
  async getProjects(filters?: {
    status?: ProjectStatus;
    clientName?: string;
    limit?: number;
    offset?: number;
  }) {
    const where: Record<string, any> = {};
    
    if (filters?.status) where.status = filters.status;
    if (filters?.clientName) {
      where.clientName = {
        contains: filters.clientName,
        mode: 'insensitive'
      };
    }

    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where,
        include: {
          photos: { take: 1 },
          analysis: { take: 1 }
        },
        orderBy: { updatedAt: 'desc' },
        take: filters?.limit || 50,
        skip: filters?.offset || 0
      }),
      prisma.project.count({ where })
    ]);

    return { projects, total };
  },

  /**
   * Update project status
   */
  async updateProjectStatus(id: string, status: ProjectStatus) {
    return await prisma.project.update({
      where: { id },
      data: { status }
    });
  },

  /**
   * Verwijder project en alle gerelateerde data
   */
  async deleteProject(id: string) {
    return await prisma.project.delete({
      where: { id }
    });
  }
};

// ===== FOTO OPERATIES =====
export const PhotoService = {
  /**
   * Upload nieuwe foto voor project
   */
  async uploadPhoto(data: {
    filename: string;
    url: string;
    projectId: string;
  }) {
    return await prisma.photo.create({
      data: {
        filename: data.filename,
        url: data.url,
        projectId: data.projectId
      }
    });
  },

  /**
   * Haal alle foto's van een project op
   */
  async getProjectPhotos(projectId: string) {
    return await prisma.photo.findMany({
      where: { projectId },
      orderBy: { createdAt: 'desc' }
    });
  },

  /**
   * Verwijder foto
   */
  async deletePhoto(id: string) {
    return await prisma.photo.delete({
      where: { id }
    });
  }
};

// ===== METINGEN OPERATIES =====
export const MeasurementService = {
  /**
   * Voeg metingen toe aan project
   */
  async addMeasurements(data: {
    length: number;
    width: number;
    height?: number;
    projectId: string;
  }) {
    // Valideer afmetingen
    if (!BusinessRules.valideerAfmetingen(data.length, data.width)) {
      throw new Error('Ongeldige afmetingen');
    }

    return await prisma.measurement.create({
      data: {
        length: data.length,
        width: data.width,
        height: data.height,
        projectId: data.projectId
      }
    });
  },

  /**
   * Bereken oppervlakte van project
   */
  async calculateProjectArea(projectId: string): Promise<number> {
    const measurements = await prisma.measurement.findMany({
      where: { projectId }
    });

    if (measurements.length === 0) return 0;

    // Gebruik de laatste meting
    const latest = measurements[measurements.length - 1];
    return BusinessRules.berekenOppervlakte(latest.length, latest.width);
  }
};

// ===== AI ANALYSE OPERATIES =====
export const AnalysisService = {
  /**
   * Sla AI analyse resultaat op
   */
  async saveAnalysis(data: {
    material: MateriaalType;
    condition: string;
    priceEstimate: number;
    confidence: number;
    projectId: string;
  }) {
    return await prisma.analysis.create({
      data: {
        material: data.material,
        condition: data.condition,
        priceEstimate: data.priceEstimate,
        confidence: data.confidence,
        projectId: data.projectId
      }
    });
  },

  /**
   * Haal laatste analyse van project op
   */
  async getLatestAnalysis(projectId: string) {
    return await prisma.analysis.findFirst({
      where: { projectId },
      orderBy: { createdAt: 'desc' }
    });
  },

  /**
   * Update project status naar VOLTOOID na succesvolle analyse
   */
  async completeAnalysis(projectId: string) {
    return await prisma.project.update({
      where: { id: projectId },
      data: { status: 'VOLTOOID' }
    });
  }
};

// ===== STATISTIEKEN =====
export const StatisticsService = {
  /**
   * Haal project statistieken op
   */
  async getProjectStats() {
    const [total, nieuw, actief, voltooid, gefactureerd] = await Promise.all([
      prisma.project.count(),
      prisma.project.count({ where: { status: 'NIEUW' } }),
      prisma.project.count({ where: { status: 'AKTIEF' } }),
      prisma.project.count({ where: { status: 'VOLTOOID' } }),
      prisma.project.count({ where: { status: 'GEFACTUREERD' } })
    ]);

    return {
      total,
      nieuw,
      actief,
      voltooid,
      gefactureerd,
      gemiddeldePrijs: await this.getAveragePrice()
    };
  },

  /**
   * Bereken gemiddelde project prijs
   */
  async getAveragePrice(): Promise<number> {
    const result = await prisma.analysis.aggregate({
      _avg: {
        priceEstimate: true
      }
    });

    return result._avg.priceEstimate || 0;
  },

  /**
   * Haal materiaal verdeling op
   */
  async getMaterialDistribution() {
    const materials = await prisma.analysis.groupBy({
      by: ['material'],
      _count: {
        material: true
      }
    });

    return materials.map(m => ({
      material: m.material,
      count: m._count.material
    }));
  }
};

// ===== DATABASE UTILITIES =====
export const DatabaseUtils = {
  /**
   * Reset database (alleen voor development)
   */
  async resetDatabase() {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Database reset niet toegestaan in productie');
    }

    await prisma.analysis.deleteMany();
    await prisma.measurement.deleteMany();
    await prisma.photo.deleteMany();
    await prisma.project.deleteMany();
  },

  /**
   * Maak test data aan
   */
  async seedTestData() {
    if (process.env.NODE_ENV === 'production') {
      throw new Error('Test data niet toegestaan in productie');
    }

    // Maak test projecten aan
    const project1 = await ProjectService.createProject({
      name: 'Woonkamer Renovatie',
      clientName: 'Jan Jansen',
      clientEmail: '<EMAIL>'
    });

    const project2 = await ProjectService.createProject({
      name: 'Badkamer Tegels',
      clientName: 'Piet Pietersen',
      clientEmail: '<EMAIL>'
    });

    // Voeg metingen toe
    await MeasurementService.addMeasurements({
      length: 5.5,
      width: 4.2,
      projectId: project1.id
    });

    await MeasurementService.addMeasurements({
      length: 3.0,
      width: 2.5,
      projectId: project2.id
    });

    // Voeg analyses toe
    await AnalysisService.saveAnalysis({
      material: MateriaalType.LAMINAAT,
      condition: 'goed',
      priceEstimate: 850.50,
      confidence: 0.85,
      projectId: project1.id
    });

    await AnalysisService.saveAnalysis({
      material: MateriaalType.TEGELS,
      condition: 'redelijk',
      priceEstimate: 1200.00,
      confidence: 0.92,
      projectId: project2.id
    });

    return { project1, project2 };
  }
};

// src/lib/utils.ts
/**
 * 🛠️ UTILITIES
 * 
 * Algemene utility functies voor de vloerwerk CRM applicatie.
 */

import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';
import { DeviceInfo } from '@/types';

// ===== TAILWIND UTILITIES =====
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

// ===== DEVICE DETECTION =====
export function detectDevice(userAgent: string): DeviceInfo {
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  const isTablet = /iPad|Android(?=.*\bMobile\b)(?=.*\bSafari\b)/i.test(userAgent);
  const isDesktop = !isMobile && !isTablet;

  return {
    isMobile,
    isTablet,
    isDesktop,
    screenWidth: typeof window !== 'undefined' ? window.innerWidth : 1024,
    userAgent
  };
}

export function getDeviceType(userAgent: string): 'mobile' | 'tablet' | 'desktop' {
  const device = detectDevice(userAgent);
  
  if (device.isMobile) return 'mobile';
  if (device.isTablet) return 'tablet';
  return 'desktop';
}

// ===== FORMATTING UTILITIES =====
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('nl-NL', {
    style: 'currency',
    currency: 'EUR'
  }).format(amount);
}

export function formatDate(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('nl-NL', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  }).format(d);
}

export function formatDateTime(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date;
  return new Intl.DateTimeFormat('nl-NL', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(d);
}

// ===== VALIDATION UTILITIES =====
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function validatePhone(phone: string): boolean {
  const phoneRegex = /^[\+]?[0-9\s\-\(\)]{8,}$/;
  return phoneRegex.test(phone);
}

export function validatePostcode(postcode: string): boolean {
  const postcodeRegex = /^[1-9][0-9]{3}\s?[A-Z]{2}$/i;
  return postcodeRegex.test(postcode);
}

// ===== FILE UTILITIES =====
export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || '';
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

export function isValidImageFile(file: File): boolean {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  return allowedTypes.includes(file.type) && file.size <= maxSize;
}

// ===== STRING UTILITIES =====
export function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
}

export function truncateText(text: string, maxLength: number): string {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + '...';
}

export function slugify(text: string): string {
  return text
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '');
}

// ===== ARRAY UTILITIES =====
export function chunkArray<T>(array: T[], size: number): T[][] {
  const chunks: T[][] = [];
  for (let i = 0; i < array.length; i += size) {
    chunks.push(array.slice(i, i + size));
  }
  return chunks;
}

export function uniqueArray<T>(array: T[]): T[] {
  return Array.from(new Set(array));
}

export function sortByProperty<T>(array: T[], property: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] {
  return [...array].sort((a, b) => {
    const aVal = a[property];
    const bVal = b[property];
    
    if (aVal < bVal) return direction === 'asc' ? -1 : 1;
    if (aVal > bVal) return direction === 'asc' ? 1 : -1;
    return 0;
  });
}

// ===== DATE UTILITIES =====
export function addDays(date: Date, days: number): Date {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
}

export function isToday(date: Date): boolean {
  const today = new Date();
  return date.toDateString() === today.toDateString();
}

export function isYesterday(date: Date): boolean {
  const yesterday = new Date();
  yesterday.setDate(yesterday.getDate() - 1);
  return date.toDateString() === yesterday.toDateString();
}

export function getDaysBetween(date1: Date, date2: Date): number {
  const oneDay = 24 * 60 * 60 * 1000; // hours*minutes*seconds*milliseconds
  return Math.round(Math.abs((date1.getTime() - date2.getTime()) / oneDay));
}

// ===== MATH UTILITIES =====
export function roundToDecimals(value: number, decimals: number = 2): number {
  return Math.round((value + Number.EPSILON) * Math.pow(10, decimals)) / Math.pow(10, decimals);
}

export function calculatePercentage(part: number, total: number): number {
  if (total === 0) return 0;
  return roundToDecimals((part / total) * 100, 2);
}

export function calculateBTW(amount: number, btwPercentage: number = 21): number {
  return roundToDecimals(amount * (btwPercentage / 100), 2);
}

export function calculateBTWInclusive(amount: number, btwPercentage: number = 21): number {
  return roundToDecimals(amount * (1 + btwPercentage / 100), 2);
}

// ===== COLOR UTILITIES =====
export function getStatusColor(status: string): string {
  const colors = {
    'NIEUW': 'bg-blue-500',
    'AKTIEF': 'bg-yellow-500',
    'VOLTOOID': 'bg-green-500',
    'GEFACTUREERD': 'bg-purple-500',
    'CONCEPT': 'bg-gray-500',
    'VERZONDEN': 'bg-orange-500',
    'BETAALD': 'bg-green-600',
    'VERVALLEN': 'bg-red-500'
  };
  
  return colors[status as keyof typeof colors] || 'bg-gray-500';
}

export function getPriorityColor(priority: number): string {
  if (priority >= 8) return 'bg-red-500';
  if (priority >= 5) return 'bg-yellow-500';
  return 'bg-green-500';
}

// ===== ERROR HANDLING =====
export function handleError(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  return 'Er is een onbekende fout opgetreden';
}

export function isNetworkError(error: unknown): boolean {
  if (error instanceof Error) {
    return error.message.includes('fetch') || 
           error.message.includes('network') ||
           error.message.includes('timeout');
  }
  return false;
}

// ===== STORAGE UTILITIES =====
export function saveToLocalStorage(key: string, value: unknown): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error('LocalStorage opslaan mislukt:', error);
    }
  }
}

export function getFromLocalStorage<T>(key: string, defaultValue: T): T {
  if (typeof window !== 'undefined') {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : defaultValue;
    } catch (error) {
      console.error('LocalStorage ophalen mislukt:', error);
      return defaultValue;
    }
  }
  return defaultValue;
}

export function removeFromLocalStorage(key: string): void {
  if (typeof window !== 'undefined') {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('LocalStorage verwijderen mislukt:', error);
    }
  }
}

// ===== DEBOUNCE UTILITY =====
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

// ===== THROTTLE UTILITY =====
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
} 
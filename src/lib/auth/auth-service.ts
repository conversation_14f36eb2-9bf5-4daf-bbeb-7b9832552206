// src/lib/auth/auth-service.ts
/**
 * 🔐 AUTHENTICATIE SERVICE
 * 
 * Centrale authenticatie service voor user management.
 * Handelt login, registratie, token management en role-based access.
 */

import { api } from '@/lib/api/client';
import { UserRole } from '@/types';

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  createdAt: Date;
  lastLogin?: Date;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name: string;
  role?: UserRole;
}

class AuthService {
  private static instance: AuthService;
  private authState: AuthState = {
    user: null,
    isAuthenticated: false,
    isLoading: false,
    error: null,
  };

  private listeners: ((state: AuthState) => void)[] = [];

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService();
    }
    return AuthService.instance;
  }

  /**
   * Subscribe to auth state changes
   */
  subscribe(listener: (state: AuthState) => void): () => void {
    this.listeners.push(listener);
    listener(this.authState);

    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  /**
   * Notify listeners of state changes
   */
  private notifyListeners() {
    this.listeners.forEach(listener => listener(this.authState));
  }

  /**
   * Update auth state
   */
  private setState(updates: Partial<AuthState>) {
    this.authState = { ...this.authState, ...updates };
    this.notifyListeners();
  }

  /**
   * Login gebruiker
   */
  async login(credentials: LoginCredentials): Promise<boolean> {
    try {
      this.setState({ isLoading: true, error: null });

      const response = await api.auth.login(credentials);

      if (response.success && response.data) {
        const { user, token } = response.data;
        
        // Sla token op
        if (typeof window !== 'undefined') {
          localStorage.setItem('auth_token', token);
        }

        this.setState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        return true;
      } else {
        this.setState({
          isLoading: false,
          error: response.error || 'Login mislukt',
        });
        return false;
      }
    } catch (error) {
      this.setState({
        isLoading: false,
        error: 'Netwerk fout tijdens login',
      });
      return false;
    }
  }

  /**
   * Registreer nieuwe gebruiker
   */
  async register(data: RegisterData): Promise<boolean> {
    try {
      this.setState({ isLoading: true, error: null });

      const response = await api.auth.register(data);

      if (response.success && response.data) {
        const { user, token } = response.data;
        
        // Sla token op
        if (typeof window !== 'undefined') {
          localStorage.setItem('auth_token', token);
        }

        this.setState({
          user,
          isAuthenticated: true,
          isLoading: false,
          error: null,
        });

        return true;
      } else {
        this.setState({
          isLoading: false,
          error: response.error || 'Registratie mislukt',
        });
        return false;
      }
    } catch (error) {
      this.setState({
        isLoading: false,
        error: 'Netwerk fout tijdens registratie',
      });
      return false;
    }
  }

  /**
   * Logout gebruiker
   */
  async logout(): Promise<void> {
    try {
      await api.auth.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // Verwijder token
      if (typeof window !== 'undefined') {
        localStorage.removeItem('auth_token');
      }

      this.setState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      });
    }
  }

  /**
   * Check of gebruiker is ingelogd
   */
  async checkAuth(): Promise<boolean> {
    try {
      const token = typeof window !== 'undefined' ? localStorage.getItem('auth_token') : null;
      
      if (!token) {
        this.setState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        });
        return false;
      }

      this.setState({ isLoading: true });

      const response = await api.users.getProfile();

      if (response.success && response.data) {
        this.setState({
          user: response.data,
          isAuthenticated: true,
          isLoading: false,
        });
        return true;
      } else {
        // Token is ongeldig
        if (typeof window !== 'undefined') {
          localStorage.removeItem('auth_token');
        }
        
        this.setState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
        });
        return false;
      }
    } catch (error) {
      this.setState({
        user: null,
        isAuthenticated: false,
        isLoading: false,
      });
      return false;
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(data: Partial<User>): Promise<boolean> {
    try {
      this.setState({ isLoading: true, error: null });

      const response = await api.users.updateProfile(data);

      if (response.success && response.data) {
        this.setState({
          user: response.data,
          isLoading: false,
          error: null,
        });
        return true;
      } else {
        this.setState({
          isLoading: false,
          error: response.error || 'Profiel update mislukt',
        });
        return false;
      }
    } catch (error) {
      this.setState({
        isLoading: false,
        error: 'Netwerk fout tijdens profiel update',
      });
      return false;
    }
  }

  /**
   * Check user permissions
   */
  hasPermission(permission: string): boolean {
    if (!this.authState.user) return false;

    const { role } = this.authState.user;

    // Role-based permissions
    const permissions = {
      [UserRole.ADMIN]: ['*'], // Admin heeft alle rechten
      [UserRole.PROJECTMANAGER]: [
        'projects.*',
        'photos.*',
        'ai.*',
        'analytics.read',
        'users.read',
      ],
      [UserRole.EMPLOYEE]: [
        'projects.read',
        'projects.update',
        'photos.upload',
        'photos.read',
      ],
      [UserRole.DEMOUSER]: [
        'projects.read',
        'photos.read',
        'analytics.read',
      ],
    };

    const userPermissions = permissions[role] || [];
    
    return userPermissions.includes('*') || userPermissions.includes(permission);
  }

  /**
   * Get current user
   */
  getCurrentUser(): User | null {
    return this.authState.user;
  }

  /**
   * Get auth state
   */
  getAuthState(): AuthState {
    return this.authState;
  }

  /**
   * Clear error
   */
  clearError(): void {
    this.setState({ error: null });
  }
}

// Export singleton instance
export const authService = AuthService.getInstance();

// Export types
export type { User, AuthState, LoginCredentials, RegisterData }; 
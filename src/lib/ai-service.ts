// src/lib/ai-service.ts
/**
 * 🤖 AI SERVICE
 * 
 * AI analyse service voor vloerwerk foto's.
 * Detecteert materiaal type, conditie en berekent prijzen.
 */

import { MateriaalType, AIAnalyseResult, VisionAnalysisResult } from '@/types';
import { MATERIAAL_PRIJZEN } from './business-rules';



// ===== AI SERVICE CLASS =====
export class AIService {
  private static instance: AIService;
  private isProcessing = false;

  static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  /**
   * Analyseer foto met AI
   */
  async analyzePhoto(imageUrl: string): Promise<AIAnalyseResult> {
    if (this.isProcessing) {
      throw new Error('AI analyse al bezig');
    }

    this.isProcessing = true;

    try {
      // Simuleer AI analyse (in productie zou dit echte AI API zijn)
      const mockResult = await this.mockAIAnalysis();
      
      // Bereken prijs op basis van analyse
      const prijsCalculatie = this.calculatePrice(mockResult);
      
      return {
        ...mockResult,
        geschatteTijd: this.calculateTime(mockResult),
        geschatteKosten: prijsCalculatie.totaal,
        aanbevolenMethode: this.getRecommendedMethod(mockResult),
        rawData: mockResult
      };

    } catch (error) {
      console.error('AI analyse mislukt:', error);
      throw new Error('AI analyse kon niet voltooid worden');
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Mock AI analyse (vervang door echte AI API)
   */
  private async mockAIAnalysis(): Promise<VisionAnalysisResult> {
    // Simuleer API delay
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Mock resultaten gebaseerd op materiaal types
    const materialTypes = Object.values(MateriaalType);
    const randomMaterial = materialTypes[Math.floor(Math.random() * materialTypes.length)] as MateriaalType;
    
    const mockResult: VisionAnalysisResult = {
      materiaalType: randomMaterial,
      conditieScore: Math.floor(Math.random() * 5) + 5, // 5-10
      complexiteitscore: Math.floor(Math.random() * 5) + 3, // 3-8
      oppervlakteType: ['GLAD', 'RUW', 'GESTRUCTUREERD'][Math.floor(Math.random() * 3)] as any,
      confidence: 0.7 + Math.random() * 0.3, // 0.7-1.0
      details: {
        kleurToon: ['lichte eik', 'donkere walnoot', 'grijs', 'wit'][Math.floor(Math.random() * 4)],
        textuur: ['glad', 'gestructureerd', 'ruw'][Math.floor(Math.random() * 3)],
        beschadigingen: Math.random() > 0.5 ? ['krassen', 'slijtage'] : [],
        oppervlakteKwaliteit: Math.floor(Math.random() * 5) + 5
      }
    };

    return mockResult;
  }

  /**
   * Bereken prijs op basis van AI analyse
   */
  private calculatePrice(analysis: VisionAnalysisResult): {
    materiaalKosten: number;
    arbeidKosten: number;
    complexiteitToeslag: number;
    subtotaal: number;
    btw: number;
    totaal: number;
  } {
    const prijzen = MATERIAAL_PRIJZEN[analysis.materiaalType];
    const oppervlakte = analysis.details.oppervlakteKwaliteit || 20; // Default 20m²
    
    // Basis kosten
    const materiaalKosten = oppervlakte * prijzen.materiaal;
    const arbeidKosten = oppervlakte * prijzen.arbeid;
    
    // Complexiteit toeslag
    const complexiteitFactor = analysis.complexiteitscore / 10;
    const complexiteitToeslag = arbeidKosten * complexiteitFactor;
    
    // Conditie korting/bonus
    const conditieFactor = analysis.conditieScore / 10;
    const conditieKorting = (materiaalKosten + arbeidKosten) * (1 - conditieFactor) * 0.2;
    
    const subtotaal = Math.max(
      materiaalKosten + arbeidKosten + complexiteitToeslag - conditieKorting,
      prijzen.minimumPrijs
    );
    
    const btw = subtotaal * 0.21;
    const totaal = subtotaal + btw;

    return {
      materiaalKosten: Math.round(materiaalKosten * 100) / 100,
      arbeidKosten: Math.round(arbeidKosten * 100) / 100,
      complexiteitToeslag: Math.round(complexiteitToeslag * 100) / 100,
      subtotaal: Math.round(subtotaal * 100) / 100,
      btw: Math.round(btw * 100) / 100,
      totaal: Math.round(totaal * 100) / 100
    };
  }

  /**
   * Bereken geschatte tijd
   */
  private calculateTime(analysis: VisionAnalysisResult): number {
    const oppervlakte = analysis.details.oppervlakteKwaliteit || 20;
    const complexiteitFactor = analysis.complexiteitscore / 10;
    
    // Basis tijd per m² (in uren)
    const basisTijdPerM2 = {
      [MateriaalType.LAMINAAT]: 0.5,
      [MateriaalType.PARKET]: 0.8,
      [MateriaalType.TEGELS]: 1.0,
      [MateriaalType.VINYL]: 0.4,
      [MateriaalType.NATUURSTEEN]: 1.5
    };

    const basisTijd = oppervlakte * basisTijdPerM2[analysis.materiaalType];
    const totaleTijd = basisTijd * complexiteitFactor + 2; // +2 uur voorbereiding
    
    return Math.max(totaleTijd, 4); // Minimum 4 uur
  }

  /**
   * Bepaal aanbevolen methode
   */
  private getRecommendedMethod(analysis: VisionAnalysisResult): string {
    const { materiaalType, conditieScore, complexiteitscore } = analysis;
    
    if (conditieScore < 5) {
      return 'Vervanging aanbevolen - vloer in slechte staat';
    }
    
    if (complexiteitscore > 7) {
      return 'Professionele installatie vereist - complex project';
    }
    
    const methoden = {
      [MateriaalType.LAMINAAT]: 'Kliksysteem installatie',
      [MateriaalType.PARKET]: 'Lijm of spijker installatie',
      [MateriaalType.TEGELS]: 'Cementmortel installatie',
      [MateriaalType.VINYL]: 'Kliksysteem of lijm installatie',
      [MateriaalType.NATUURSTEEN]: 'Cementmortel installatie'
    };
    
    return methoden[materiaalType] || 'Standaard installatie';
  }

  /**
   * Batch analyse van meerdere foto's
   */
  async analyzeMultiplePhotos(photoUrls: string[]): Promise<AIAnalyseResult[]> {
    const results: AIAnalyseResult[] = [];
    
    for (const photoUrl of photoUrls) {
      try {
        const result = await this.analyzePhoto(photoUrl);
        results.push(result);
      } catch (error) {
        console.error(`Analyse mislukt voor foto: ${photoUrl}`, error);
        // Ga door met volgende foto
      }
    }
    
    return results;
  }

  /**
   * Valideer AI resultaat
   */
  validateAnalysisResult(result: AIAnalyseResult): boolean {
    return (
      result.confidence >= 0.7 &&
      result.conditieScore >= 1 && result.conditieScore <= 10 &&
      result.complexiteitscore >= 1 && result.complexiteitscore <= 10 &&
      Object.values(MateriaalType).includes(result.materiaalType)
    );
  }

  /**
   * Cache AI resultaat
   */
  async cacheAnalysisResult(projectId: string): Promise<void> {
    try {
      // Hier zou je het resultaat kunnen cachen in database
      console.log(`AI analyse gecached voor project: ${projectId}`);
    } catch (error) {
      console.error('Cache mislukt:', error);
    }
  }

  /**
   * Haal gecached resultaat op
   */
  async getCachedAnalysis(projectId: string): Promise<AIAnalyseResult | null> {
    try {
      // Hier zou je het gecachte resultaat kunnen ophalen
      return null; // Voor nu geen cache
    } catch (error) {
      console.error('Cache ophalen mislukt:', error);
      return null;
    }
  }
}

// ===== EXPORT SINGLETON =====
export const aiService = AIService.getInstance();

export enum MateriaalType {
  LAMINAAT = 'LAMINAAT',
  PARKET = 'PARKET',
  TEGELS = 'TEGELS',
  VINYL = 'VINYL',
  TAPIJT = 'TAPIJT',
  NATUURSTEEN = 'NATUURSTEEN'
}

// Backward compatibility
export const MaterialType = MateriaalType;

export enum OppervlakteType {
  GLAD = 'GLAD',
  RUW = 'RUW',
  GESTRUCTUREERD = 'GESTRUCTUREERD'
}

export enum ProjectStatus {
  NIEUW = 'NIEUW',
  AKTIEF = 'AKTIEF',
  VOLTOOID = 'VOLTOOID',
  GEFACTUREERD = 'GEFACTUREERD'
}

export enum FactuurStatus {
  CONCEPT = 'CONCEPT',
  VERZONDEN = 'VERZONDEN',
  BETAALD = 'BETAALD',
  VERVALLEN = 'VERVALLEN'
}

export enum TaakStatus {
  OPEN = 'OPEN',
  BEZIG = 'BEZIG',
  VOLTOOID = 'VOLTOOID'
}

export interface AfmetingenInput {
  lengte: number;
  breedte: number;
  hoogte?: number;
}

export interface AIAnalyseResult {
  materiaalType: MateriaalType;
  conditieScore: number;
  complexiteitscore: number;
  oppervlakteType: OppervlakteType;
  aanbevolenMethode: string;
  geschatteTijd: number;
  geschatteKosten: number;
  confidence: number;
  rawData?: unknown;
}

export interface KlantGegevens {
  id?: string;
  naam: string;
  email?: string;
  telefoon?: string;
  adres?: string;
  postcode?: string;
  plaats?: string;
}

export interface VloerProject {
  id?: string;
  naam: string;
  beschrijving?: string;
  klantId: string;
  klant?: KlantGegevens;
  status: ProjectStatus;
  lengte?: number;
  breedte?: number;
  hoogte?: number;
  oppervlakte?: number;
  aiAnalyse?: AIAnalyseResult;
  geschattePrijs?: number;
  fotos: ProjectFoto[];
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ProjectFoto {
  id?: string;
  filename: string;
  url: string;
  description?: string;
  aiAnalyse?: string;
  createdAt?: Date;
}

export interface Factuur {
  id?: string;
  nummer: string;
  datum: Date;
  vervaldatum: Date;
  subtotaal: number;
  btw: number;
  totaal: number;
  status: FactuurStatus;
  projectId: string;
  klantId: string;
  regels: FactuurRegel[];
}

export interface FactuurRegel {
  id?: string;
  beschrijving: string;
  aantal: number;
  eenheidsprijs: number;
  totaal: number;
}

export interface Taak {
  id?: string;
  titel: string;
  beschrijving?: string;
  status: TaakStatus;
  prioriteit: number;
  deadline?: Date;
  projectId: string;
}

// Form Types
export interface ProjectFormData {
  klant: KlantGegevens;
  projectNaam: string;
  beschrijving?: string;
  afmetingen: AfmetingenInput;
}

export interface CameraConfig {
  quality: number;
  maxWidth: number;
  maxHeight: number;
  format: 'jpeg' | 'png' | 'webp';
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Device Detection
export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  userAgent: string;
}

// Pricing Types
export interface PrijsCalculatie {
  materiaalKosten: number;
  arbeidKosten: number;
  complexiteitToeslag: number;
  subtotaal: number;
  btw: number;
  totaal: number;
}

// AI Analysis Types
export interface VisionAnalysisResult {
  materiaalType: MateriaalType;
  conditieScore: number;
  complexiteitscore: number;
  oppervlakteType: OppervlakteType;
  confidence: number;
  details: {
    kleurToon: string;
    textuur: string;
    beschadigingen: string[];
    oppervlakteKwaliteit: number;
  };
}

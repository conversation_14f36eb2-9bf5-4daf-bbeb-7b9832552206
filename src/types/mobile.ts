// src/types/mobile.ts
/**
 * 📱 MOBILE TYPES
 * 
 * TypeScript types specifiek voor mobile PWA functionaliteit.
 */

import { ProjectStatus, MateriaalType } from './index';

// ===== MOBILE PROJECT TYPES =====
export interface MobileProject {
  id: string;
  name: string;
  clientName: string;
  status: ProjectStatus;
  photos: MobilePhoto[];
  analysis?: MobileAnalysis;
  offline: boolean;
  lastSync?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface MobilePhoto {
  id: string;
  filename: string;
  localUrl: string;
  remoteUrl?: string;
  uploaded: boolean;
  projectId: string;
  createdAt: Date;
  size: number;
  metadata?: PhotoMetadata;
}

export interface PhotoMetadata {
  width: number;
  height: number;
  format: string;
  size: number;
  location?: {
    latitude: number;
    longitude: number;
  };
  timestamp: Date;
}

export interface MobileAnalysis {
  id: string;
  material: MateriaalType;
  condition: string;
  priceEstimate: number;
  confidence: number;
  projectId: string;
  createdAt: Date;
  offline: boolean;
  uploaded: boolean;
}

// ===== CAMERA TYPES =====
export interface CameraConfig {
  quality: number;
  maxWidth: number;
  maxHeight: number;
  format: 'jpeg' | 'png' | 'webp';
  flash: 'auto' | 'on' | 'off';
  facingMode: 'user' | 'environment';
}

export interface CameraState {
  isActive: boolean;
  isCapturing: boolean;
  hasPermission: boolean;
  error: string | null;
  stream: MediaStream | null;
}

export interface PhotoCapture {
  blob: Blob;
  filename: string;
  metadata: PhotoMetadata;
  compressed?: Blob;
}

// ===== OFFLINE TYPES =====
export interface OfflineState {
  isOnline: boolean;
  isOffline: boolean;
  lastSync: Date | null;
  pendingUploads: number;
  pendingDownloads: number;
  storageUsed: number;
  storageLimit: number;
}

export interface SyncItem {
  id: string;
  type: 'project' | 'photo' | 'analysis';
  action: 'create' | 'update' | 'delete';
  data: any;
  timestamp: Date;
  retryCount: number;
}

export interface SyncQueue {
  items: SyncItem[];
  isProcessing: boolean;
  lastProcessed: Date | null;
  error: string | null;
}

// ===== PWA TYPES =====
export interface PWAConfig {
  name: string;
  shortName: string;
  description: string;
  startUrl: string;
  display: 'standalone' | 'fullscreen' | 'minimal-ui' | 'browser';
  backgroundColor: string;
  themeColor: string;
  icons: PWAIcon[];
}

export interface PWAIcon {
  src: string;
  sizes: string;
  type: string;
  purpose?: string;
}

export interface InstallPrompt {
  isAvailable: boolean;
  isInstalled: boolean;
  prompt: () => Promise<void>;
}

// ===== NAVIGATION TYPES =====
export interface MobileNavigationItem {
  id: string;
  label: string;
  icon: string;
  route: string;
  badge?: number;
  disabled?: boolean;
}

export interface MobileNavigationState {
  activeTab: string;
  items: MobileNavigationItem[];
  isVisible: boolean;
}

// ===== SETTINGS TYPES =====
export interface MobileSettings {
  camera: {
    quality: number;
    maxWidth: number;
    maxHeight: number;
    format: 'jpeg' | 'png' | 'webp';
    flash: 'auto' | 'on' | 'off';
  };
  offline: {
    enabled: boolean;
    autoSync: boolean;
    syncInterval: number;
    storageLimit: number;
  };
  notifications: {
    enabled: boolean;
    uploadComplete: boolean;
    syncComplete: boolean;
    errorAlerts: boolean;
  };
  privacy: {
    locationEnabled: boolean;
    analyticsEnabled: boolean;
    crashReporting: boolean;
  };
}

// ===== API RESPONSE TYPES =====
export interface MobileApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  offline?: boolean;
  syncRequired?: boolean;
}

export interface MobileProjectResponse extends MobileApiResponse<MobileProject> {}
export interface MobilePhotoResponse extends MobileApiResponse<MobilePhoto> {}
export interface MobileAnalysisResponse extends MobileApiResponse<MobileAnalysis> {}

// ===== ERROR TYPES =====
export interface MobileError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  retryable: boolean;
}

export enum MobileErrorCode {
  CAMERA_PERMISSION_DENIED = 'CAMERA_PERMISSION_DENIED',
  CAMERA_NOT_AVAILABLE = 'CAMERA_NOT_AVAILABLE',
  STORAGE_FULL = 'STORAGE_FULL',
  NETWORK_ERROR = 'NETWORK_ERROR',
  SYNC_FAILED = 'SYNC_FAILED',
  UPLOAD_FAILED = 'UPLOAD_FAILED',
  OFFLINE_MODE = 'OFFLINE_MODE',
}

// ===== UTILITY TYPES =====
export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isPWA: boolean;
  platform: 'ios' | 'android' | 'web';
  version: string;
  userAgent: string;
}

export interface StorageInfo {
  used: number;
  available: number;
  total: number;
  quota?: number;
}

export interface NetworkInfo {
  isOnline: boolean;
  type: 'wifi' | 'cellular' | 'none';
  speed?: 'slow' | 'fast' | 'unknown';
} 
// src/types/auth.ts
/**
 * 🔐 AUTH TYPES
 * 
 * TypeScript types voor authenticatie en user management.
 */

export enum UserRole {
  ADMIN = 'ADMIN',
  PROJECTMANAGER = 'PROJECTMANAGER',
  EMPLOYEE = 'EMPLOYEE',
  DEMOUSER = 'DEMOUSER'
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
  lastLogin?: Date;
  isActive: boolean;
  preferences?: UserPreferences;
}

export interface UserPreferences {
  language: 'nl' | 'fr' | 'en';
  theme: 'light' | 'dark' | 'auto';
  notifications: {
    email: boolean;
    push: boolean;
    sms: boolean;
  };
  dashboard: {
    defaultView: 'mobile' | 'desktop';
    showAnalytics: boolean;
    showRecentProjects: boolean;
  };
}

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  name: string;
  role?: UserRole;
  acceptTerms: boolean;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken?: string;
  expiresIn: number;
}

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetConfirm {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface UpdateProfileRequest {
  name?: string;
  email?: string;
  preferences?: Partial<UserPreferences>;
}

// Permission types
export type Permission = 
  | 'auth.*'
  | 'users.*'
  | 'users.read'
  | 'users.create'
  | 'users.update'
  | 'users.delete'
  | 'projects.*'
  | 'projects.read'
  | 'projects.create'
  | 'projects.update'
  | 'projects.delete'
  | 'photos.*'
  | 'photos.read'
  | 'photos.upload'
  | 'photos.delete'
  | 'ai.*'
  | 'ai.analyze'
  | 'ai.models'
  | 'analytics.*'
  | 'analytics.read'
  | 'analytics.export'
  | 'settings.*'
  | 'settings.read'
  | 'settings.update';

export interface PermissionCheck {
  permission: Permission;
  resource?: string;
  action?: string;
}

// Role permissions mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    'auth.*',
    'users.*',
    'projects.*',
    'photos.*',
    'ai.*',
    'analytics.*',
    'settings.*',
  ],
  [UserRole.PROJECTMANAGER]: [
    'projects.*',
    'photos.*',
    'ai.*',
    'analytics.read',
    'users.read',
    'settings.read',
  ],
  [UserRole.EMPLOYEE]: [
    'projects.read',
    'projects.update',
    'photos.upload',
    'photos.read',
    'analytics.read',
  ],
  [UserRole.DEMOUSER]: [
    'projects.read',
    'photos.read',
    'analytics.read',
  ],
};

// Auth state types
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  lastActivity?: Date;
}

// Session types
export interface Session {
  id: string;
  userId: string;
  token: string;
  refreshToken: string;
  expiresAt: Date;
  createdAt: Date;
  lastActivity: Date;
  userAgent?: string;
  ipAddress?: string;
}

// API Response types
export interface AuthApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface LoginApiResponse extends AuthApiResponse<AuthResponse> {}
export interface RegisterApiResponse extends AuthApiResponse<AuthResponse> {}
export interface ProfileApiResponse extends AuthApiResponse<User> {}
export interface UsersApiResponse extends AuthApiResponse<User[]> {} 
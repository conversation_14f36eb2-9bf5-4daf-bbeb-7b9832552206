// src/app/mobile/page.tsx
/**
 * 📱 MOBILE HOME
 * 
 * Mobile home pagina met project overzicht en snelle acties.
 * Touch-friendly interface voor veldwerkers.
 */

'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { api } from '@/lib/api/client';
import { MobileProject, ProjectStatus } from '@/types/mobile';
import { formatCurrency, formatDate } from '@/lib/utils';

export default function MobileHomePage() {
  const router = useRouter();
  const [projects, setProjects] = useState<MobileProject[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [offlineMode, setOfflineMode] = useState(false);

  // ===== DATA LOADING =====
  useEffect(() => {
    loadProjects();
    checkOnlineStatus();
  }, []);

  const loadProjects = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await api.projects.getAll({ limit: 20 });
      
      if (response.success && response.data) {
        setProjects(response.data.projects || []);
      } else {
        setError(response.error || 'Projecten laden mislukt');
      }
    } catch (error) {
      console.error('Projects load error:', error);
      setError('Netwerk fout');
      setOfflineMode(true);
    } finally {
      setIsLoading(false);
    }
  };

  const checkOnlineStatus = () => {
    const isOnline = navigator.onLine;
    setOfflineMode(!isOnline);

    // Listen for online/offline events
    const handleOnline = () => setOfflineMode(false);
    const handleOffline = () => setOfflineMode(true);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  };

  // ===== QUICK ACTIONS =====
  const handleNewProject = () => {
    router.push('/mobile/project/new');
  };

  const handleOpenCamera = () => {
    router.push('/mobile/camera');
  };

  const handleProjectClick = (projectId: string) => {
    router.push(`/mobile/project/${projectId}`);
  };

  // ===== LOADING STATE =====
  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600">Projecten laden...</p>
        </div>
      </div>
    );
  }

  // ===== ERROR STATE =====
  if (error && projects.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="text-6xl mb-4">⚠️</div>
          <h1 className="text-xl font-bold text-gray-800 mb-2">Fout bij laden</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={loadProjects}
            className="bg-blue-500 text-white px-6 py-2 rounded-lg"
          >
            Opnieuw proberen
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-lg font-semibold text-gray-900">
                Vloerwerk CRM
              </h1>
              <p className="text-sm text-gray-600">
                Mobile App
              </p>
            </div>
            <div className="flex items-center space-x-2">
              {offlineMode && (
                <div className="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs">
                  Offline
                </div>
              )}
              <Link
                href="/mobile/settings"
                className="text-gray-500 hover:text-gray-700"
              >
                ⚙️
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="p-4 bg-white border-b">
        <div className="grid grid-cols-2 gap-3">
          <button
            onClick={handleNewProject}
            className="bg-blue-500 text-white p-4 rounded-lg text-center"
          >
            <div className="text-2xl mb-2">📋</div>
            <div className="text-sm font-medium">Nieuw Project</div>
          </button>
          
          <button
            onClick={handleOpenCamera}
            className="bg-green-500 text-white p-4 rounded-lg text-center"
          >
            <div className="text-2xl mb-2">📸</div>
            <div className="text-sm font-medium">Camera</div>
          </button>
        </div>
      </div>

      {/* Projects List */}
      <div className="p-4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900">
            Projecten ({projects.length})
          </h2>
          <button
            onClick={loadProjects}
            className="text-blue-500 text-sm"
          >
            Ververs
          </button>
        </div>

        {projects.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-4xl mb-4">📋</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Geen projecten
            </h3>
            <p className="text-gray-600 mb-4">
              Maak je eerste project aan om te beginnen
            </p>
            <button
              onClick={handleNewProject}
              className="bg-blue-500 text-white px-6 py-3 rounded-lg font-medium"
            >
              Nieuw Project
            </button>
          </div>
        ) : (
          <div className="space-y-3">
            {projects.map((project) => (
              <div
                key={project.id}
                onClick={() => handleProjectClick(project.id)}
                className="bg-white rounded-lg p-4 shadow-sm border border-gray-200 active:bg-gray-50"
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-gray-900">
                      {project.name}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {project.clientName}
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                      <span>📸 {project.photos.length} foto's</span>
                      {project.analysis && (
                        <span>💰 {formatCurrency(project.analysis.priceEstimate)}</span>
                      )}
                      <span>📅 {formatDate(project.createdAt)}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      project.status === ProjectStatus.VOLTOOID ? 'bg-green-100 text-green-800' :
                      project.status === ProjectStatus.AKTIEF ? 'bg-blue-100 text-blue-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {project.status}
                    </span>
                    {project.offline && (
                      <span className="text-yellow-500 text-xs">Offline</span>
                    )}
                    <div className="text-gray-400">→</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div className="flex items-center justify-around py-2">
          <button className="flex flex-col items-center p-2 text-blue-500">
            <div className="text-xl">🏠</div>
            <span className="text-xs">Home</span>
          </button>
          
          <button
            onClick={handleOpenCamera}
            className="flex flex-col items-center p-2 text-gray-500"
          >
            <div className="text-xl">📸</div>
            <span className="text-xs">Camera</span>
          </button>
          
          <button
            onClick={handleNewProject}
            className="flex flex-col items-center p-2 text-gray-500"
          >
            <div className="text-xl">➕</div>
            <span className="text-xs">Nieuw</span>
          </button>
          
          <Link
            href="/mobile/settings"
            className="flex flex-col items-center p-2 text-gray-500"
          >
            <div className="text-xl">⚙️</div>
            <span className="text-xs">Instellingen</span>
          </Link>
        </div>
      </div>

      {/* Offline Banner */}
      {offlineMode && (
        <div className="fixed top-0 left-0 right-0 bg-yellow-500 text-white px-4 py-2 text-center text-sm">
          Je werkt offline - wijzigingen worden gesynchroniseerd bij verbinding
        </div>
      )}
    </div>
  );
}

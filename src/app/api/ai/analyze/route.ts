// src/app/api/ai/analyze/route.ts
/**
 * 🤖 AI ANALYSE API
 * 
 * API route voor AI analyse van vloerwerk foto's.
 * Detecteert materiaal type, conditie en berekent prijzen.
 */

import { NextRequest, NextResponse } from 'next/server';
import { aiService } from '@/lib/ai-service';
import { AnalysisService, ProjectService } from '@/lib/db';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { photoUrl, projectId } = body;

    // Valideer input
    if (!photoUrl || !projectId) {
      return NextResponse.json(
        { success: false, error: 'Foto URL en project ID zijn verplicht' },
        { status: 400 }
      );
    }

    // Controleer of project bestaat
    const project = await ProjectService.getProject(projectId);
    if (!project) {
      return NextResponse.json(
        { success: false, error: 'Project niet gevonden' },
        { status: 404 }
      );
    }

    // Voer AI analyse uit
    const analysisResult = await aiService.analyzePhoto(photoUrl);

    // Valideer AI resultaat
    if (!aiService.validateAnalysisResult(analysisResult)) {
      return NextResponse.json(
        { success: false, error: 'AI analyse resultaat ongeldig' },
        { status: 400 }
      );
    }

    // Sla analyse op in database
    const savedAnalysis = await AnalysisService.saveAnalysis({
      material: analysisResult.materiaalType,
      condition: `Score: ${analysisResult.conditieScore}/10`,
      priceEstimate: analysisResult.geschatteKosten,
      confidence: analysisResult.confidence,
      projectId
    });

    // Update project status naar VOLTOOID als analyse succesvol is
    if (analysisResult.confidence >= 0.7) {
      await AnalysisService.completeAnalysis(projectId);
    }

    // Cache het resultaat
    await aiService.cacheAnalysisResult(projectId, analysisResult);

    return NextResponse.json({
      success: true,
      data: {
        analysis: analysisResult,
        databaseId: savedAnalysis.id,
        projectStatus: 'VOLTOOID'
      }
    });

  } catch (error) {
    console.error('AI analyse mislukt:', error);
    return NextResponse.json(
      { success: false, error: 'AI analyse kon niet voltooid worden' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { success: false, error: 'GET niet ondersteund' },
    { status: 405 }
  );
} 
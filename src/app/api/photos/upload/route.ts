// src/app/api/photos/upload/route.ts
/**
 * 📸 FOTO UPLOAD API
 * 
 * API route voor het uploaden van vloerwerk foto's.
 * Ondersteunt bestandsvalidatie en database opslag.
 */

import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { PhotoService } from '@/lib/db';

// ===== CONFIGURATIE =====
const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  uploadDir: join(process.cwd(), 'public', 'uploads'),
  maxPhotosPerProject: 10
};

export async function POST(request: NextRequest) {
  try {
    // Parse multipart form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const projectId = formData.get('projectId') as string;

    // Valideer input
    if (!file || !projectId) {
      return NextResponse.json(
        { success: false, error: 'Bestand en project ID zijn verplicht' },
        { status: 400 }
      );
    }

    // Valideer bestandsgrootte
    if (file.size > UPLOAD_CONFIG.maxFileSize) {
      return NextResponse.json(
        { success: false, error: 'Bestand is te groot (max 10MB)' },
        { status: 400 }
      );
    }

    // Valideer bestandstype
    if (!UPLOAD_CONFIG.allowedTypes.includes(file.type)) {
      return NextResponse.json(
        { success: false, error: 'Ongeldig bestandstype' },
        { status: 400 }
      );
    }

    // Controleer aantal foto's per project
    const existingPhotos = await PhotoService.getProjectPhotos(projectId);
    if (existingPhotos.length >= UPLOAD_CONFIG.maxPhotosPerProject) {
      return NextResponse.json(
        { success: false, error: 'Maximum aantal foto\'s bereikt' },
        { status: 400 }
      );
    }

    // Maak upload directory aan
    await mkdir(UPLOAD_CONFIG.uploadDir, { recursive: true });

    // Genereer unieke bestandsnaam
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const filename = `vloerwerk-${projectId}-${timestamp}.${fileExtension}`;
    const filepath = join(UPLOAD_CONFIG.uploadDir, filename);

    // Converteer file naar buffer en schrijf naar disk
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filepath, buffer);

    // Sla foto op in database
    const photo = await PhotoService.uploadPhoto({
      filename,
      url: `/uploads/${filename}`,
      projectId
    });

    return NextResponse.json({
      success: true,
      data: {
        id: photo.id,
        filename: photo.filename,
        url: photo.url,
        createdAt: photo.createdAt
      }
    });

  } catch (error) {
    console.error('Foto upload mislukt:', error);
    return NextResponse.json(
      { success: false, error: 'Upload mislukt' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { success: false, error: 'GET niet ondersteund' },
    { status: 405 }
  );
} 
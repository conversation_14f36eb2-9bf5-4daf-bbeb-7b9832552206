/**
 * 🏠 MOBILE-FIRST LAYOUT
 * 
 * Hoofdlayout geoptimaliseerd voor mobile development.
 * Geen device detection - direct naar mobile interface.
 */

import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import './globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata: Metadata = {
  title: 'Vloerwerk CRM - Mobile App',
  description: 'Mobile-first CRM voor vloerwerk bedrijven',
  manifest: '/manifest.json',
  viewport: 'width=device-width, initial-scale=1, maximum-scale=1',
  themeColor: '#3B82F6'
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="nl">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}

// src/app/page.tsx
/**
 * 🏠 MOBILE-FIRST LANDING PAGE
 * 
 * Landing page die direct doorstuurt naar mobile interface.
 * Geoptimaliseerd voor mobile-first development.
 */

import Link from 'next/link';

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-4xl mx-auto px-4 py-16">
        <div className="text-center">
          {/* Logo en Titel */}
          <div className="mb-8">
            <div className="text-6xl mb-4">🏠</div>
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Vloerwerk CRM
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Mobile-first CRM voor vloerwerk bedrijven
            </p>
          </div>

          {/* Mobile-First Features */}
          <div className="grid md:grid-cols-3 gap-8 mb-12">
            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="text-3xl mb-4">📸</div>
              <h3 className="text-lg font-semibold mb-2">Camera Capture</h3>
              <p className="text-gray-600">
                Maak foto's van vloeren met je smartphone camera
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="text-3xl mb-4">🤖</div>
              <h3 className="text-lg font-semibold mb-2">AI Analyse</h3>
              <p className="text-gray-600">
                Automatische materiaal detectie en prijsberekening
              </p>
            </div>

            <div className="bg-white rounded-lg p-6 shadow-sm">
              <div className="text-3xl mb-4">📱</div>
              <h3 className="text-lg font-semibold mb-2">Offline Werken</h3>
              <p className="text-gray-600">
                Werk zonder internet, synchroniseer later
              </p>
            </div>
          </div>

          {/* Mobile App CTA */}
          <div className="bg-white rounded-lg p-8 shadow-sm">
            <h2 className="text-2xl font-bold mb-6">Start met Mobile App</h2>
            <p className="text-gray-600 mb-6">
              De mobile app is geoptimaliseerd voor veldwerkers en werkt perfect op smartphones en tablets.
            </p>
            
            <Link
              href="/mobile"
              className="inline-block bg-blue-500 text-white px-8 py-4 rounded-lg font-medium text-lg hover:bg-blue-600 transition-colors"
            >
              <div className="flex items-center justify-center space-x-2">
                <span className="text-2xl">📱</span>
                <span>Open Mobile App</span>
              </div>
            </Link>

            <div className="mt-6 text-sm text-gray-500">
              <p>• Touch-friendly interface</p>
              <p>• Camera functionaliteit</p>
              <p>• Offline mogelijkheden</p>
              <p>• PWA installatie</p>
            </div>
          </div>

          {/* Development Info */}
          <div className="mt-12 text-center text-gray-500">
            <p className="text-sm">
              Mobile-first development - Desktop interface komt later
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

// src/config/app-config.ts
/**
 * 🔧 VLOERWERK CRM - APPLICATIE CONFIGURATIE
 * 
 * Centrale configuratie voor de gehele applicatie.
 * Wij<PERSON> deze instellingen om het gedrag van de app aan te passen.
 */

// ===== ALGEMENE APP INSTELLINGEN =====
export const APP_CONFIG = {
  name: 'Vloerwerk CRM',
  version: '1.0.0',
  description: 'AI-powered CRM voor vloerwerk bedrijven',
  
  // Taal en lokalisatie
  defaultLanguage: 'nl',
  currency: 'EUR',
  dateFormat: 'dd-MM-yyyy',
  timeFormat: '24h',
  
  // Contact informatie
  company: {
    name: 'Uw Vloerwerk Bedrijf',
    email: '<EMAIL>',
    telefoon: '+31 6 12345678',
    website: 'www.uwbedrijf.nl'
  }
} as const;

// ===== DATABASE CONFIGURATIE =====
export const DATABASE_CONFIG = {
  // Paginering
  defaultPageSize: 20,
  maxPageSize: 100,
  
  // Cache instellingen
  cacheTimeout: 300, // 5 minuten
  
  // Backup instellingen
  autoBackup: true,
  backupInterval: 24 // uren
} as const;

// ===== AI CONFIGURATIE =====
export const AI_CONFIG = {
  // Analyse instellingen
  maxAnalysisTime: 30,        // seconden
  minConfidenceScore: 0.7,    // 70% minimum
  maxRetries: 3,              // aantal pogingen
  
  // Foto instellingen
  maxPhotos: 10,              // maximum aantal foto's
  maxFileSize: 10,            // MB per foto
  allowedFormats: ['jpeg', 'jpg', 'png', 'webp'],
  
  // Image processing
  imageQuality: 0.8,          // 80% kwaliteit
  maxImageWidth: 1920,        // pixels
  maxImageHeight: 1080,       // pixels
  
  // API instellingen
  timeout: 30000,             // 30 seconden
  rateLimit: 100              // requests per uur
} as const;

// ===== CAMERA CONFIGURATIE =====
export const CAMERA_CONFIG = {
  // Camera instellingen
  facingMode: 'environment',   // back camera
  quality: 0.8,               // 80% kwaliteit
  maxWidth: 1920,             // pixels
  maxHeight: 1080,            // pixels
  format: 'jpeg' as const,    // bestandsformaat
  
  // UI instellingen
  showPreview: true,          // toon preview
  allowRetake: true,          // opnieuw maken toegestaan
  autoSave: false,            // automatisch opslaan
  
  // Fallback opties
  enableFileInput: true,      // file input als fallback
  enableDragDrop: true        // drag & drop ondersteuning
} as const;

// ===== PRIJZEN CONFIGURATIE =====
export const PRICING_CONFIG = {
  // BTW instellingen
  btw: {
    percentage: 21,           // 21% BTW
    included: true            // prijzen inclusief BTW
  },
  
  // Afrondingsregels
  rounding: {
    decimals: 2,              // 2 decimalen
    method: 'round' as const  // round, floor, ceil
  },
  
  // Kortingen
  discounts: {
    maxPercentage: 25,        // maximum 25% korting
    loyaltyDiscount: 5,       // 5% klantkorting
    volumeDiscount: 10        // 10% volume korting (>100m²)
  },
  
  // Toeslagen
  surcharges: {
    urgentWork: 25,           // 25% toeslag spoed
    weekendWork: 50,          // 50% toeslag weekend
    eveningWork: 30           // 30% toeslag avond
  }
} as const;

// ===== VALIDATIE CONFIGURATIE =====
export const VALIDATION_CONFIG = {
  // Project validatie
  project: {
    minSurface: 5,            // minimum 5m²
    maxSurface: 500,          // maximum 500m²
    minLength: 1,             // minimum 1m
    maxLength: 50,            // maximum 50m
    minWidth: 1,              // minimum 1m
    maxWidth: 50              // maximum 50m
  },
  
  // Klant validatie
  customer: {
    minNameLength: 2,         // minimum 2 karakters
    maxNameLength: 100,       // maximum 100 karakters
    requireEmail: false,      // email verplicht
    requirePhone: true        // telefoon verplicht
  },
  
  // Bestand validatie
  files: {
    maxSize: 10,              // MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp'],
    requireDescription: false  // beschrijving verplicht
  }
} as const;

// ===== UI CONFIGURATIE =====
export const UI_CONFIG = {
  // Thema instellingen
  theme: {
    primary: '#3B82F6',       // blauw
    secondary: '#10B981',     // groen
    accent: '#F59E0B',        // oranje
    danger: '#EF4444',        // rood
    dark: '#1F2937'           // donkergrijs
  },
  
  // Layout instellingen
  layout: {
    sidebarWidth: 280,        // pixels
    headerHeight: 64,         // pixels
    footerHeight: 48,         // pixels
    containerMaxWidth: 1200   // pixels
  },
  
  // Animatie instellingen
  animations: {
    enabled: true,            // animaties aan
    duration: 300,            // milliseconden
    easing: 'ease-in-out'     // easing functie
  },
  
  // Responsive breakpoints
  breakpoints: {
    mobile: 640,              // pixels
    tablet: 768,              // pixels
    desktop: 1024,            // pixels
    wide: 1280                // pixels
  }
} as const;

// ===== NOTIFICATIE CONFIGURATIE =====
export const NOTIFICATION_CONFIG = {
  // Toast instellingen
  toast: {
    duration: 5000,           // 5 seconden
    position: 'top-right' as const,
    maxVisible: 3             // maximum aantal tegelijk
  },
  
  // Email notificaties
  email: {
    enabled: true,            // email notificaties aan
    templates: {
      newProject: 'new-project-template',
      invoiceSent: 'invoice-sent-template',
      paymentReceived: 'payment-received-template'
    }
  },
  
  // Push notificaties
  push: {
    enabled: false,           // push notificaties uit
    vapidKey: process.env.VAPID_PUBLIC_KEY || ''
  }
} as const;

// ===== EXPORT CONFIGURATIE =====
export const EXPORT_CONFIG = {
  // PDF instellingen
  pdf: {
    format: 'A4' as const,    // papierformaat
    orientation: 'portrait' as const, // staand
    margin: 20,               // mm
    quality: 1.0              // 100% kwaliteit
  },
  
  // Excel instellingen
  excel: {
    sheetName: 'Vloerwerk Data',
    includeHeaders: true,     // headers meenemen
    dateFormat: 'dd-mm-yyyy'  // datum formaat
  },
  
  // Backup instellingen
  backup: {
    includeImages: false,     // afbeeldingen meenemen
    compression: true,        // compressie gebruiken
    encryption: false         // encryptie gebruiken
  }
} as const;

// ===== DEVELOPMENT CONFIGURATIE =====
export const DEV_CONFIG = {
  // Debug instellingen
  debug: process.env.NODE_ENV === 'development',
  verbose: false,             // uitgebreide logging
  
  // Mock data
  useMockData: process.env.NODE_ENV === 'development',
  mockDelay: 1000,           // milliseconden
  
  // Testing
  enableTestMode: false,      // test modus
  skipValidation: false       // validatie overslaan
} as const;

// ===== HELPER FUNCTIES =====
export const ConfigHelpers = {
  /**
   * Krijg volledige configuratie
   */
  getFullConfig() {
    return {
      app: APP_CONFIG,
      database: DATABASE_CONFIG,
      ai: AI_CONFIG,
      camera: CAMERA_CONFIG,
      pricing: PRICING_CONFIG,
      validation: VALIDATION_CONFIG,
      ui: UI_CONFIG,
      notifications: NOTIFICATION_CONFIG,
      export: EXPORT_CONFIG,
      development: DEV_CONFIG
    };
  },

  /**
   * Check of feature enabled is
   */
  isFeatureEnabled(): boolean {
    // Implementeer feature flags hier
    return true;
  },

  /**
   * Krijg environment specifieke config
   */
  getEnvironmentConfig() {
    const env = process.env.NODE_ENV || 'development';
    
    return {
      isDevelopment: env === 'development',
      isProduction: env === 'production',
      isTest: env === 'test'
    };
  }
};

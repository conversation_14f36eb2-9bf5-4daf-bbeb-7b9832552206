Ik heb een Next.js 14 TypeScript project voor een Vloerwerk CRM applicatie. 

HUIDIGE STATUS:
✅ Database schema klaar (Prisma)
✅ Types gedefinieerd 
✅ AI service logica compleet
❌ Geen werkende app files - NIETS WERKT

PROBLEEM:
Wanneer ik `npm run dev` start krijg ik errors omdat er geen basis files zijn.

TAAK:
Maak de volgende CORE FILES zodat de app start:

1. src/app/layout.tsx (root layout met metadata)
2. src/app/page.tsx (homepage met device detection)
3. src/app/globals.css (Tailwind + custom styles)
4. src/lib/db.ts (Prisma database connection)
5. src/lib/utils.ts (utility functions)

REQUIREMENTS:
- Gebruik bestaande types uit src/types/index.ts
- Mobile-first responsive design
- Nederlandse teksten
- TypeScript strict mode
- Tailwind CSS styling
- Error boundaries
- Loading states

TECH STACK:
- Next.js 14 (app directory)
- TypeScript
- Tailwind CSS
- Prisma (SQLite)
- Lucide React icons

START MET: src/app/layout.tsx

Maak een complete, werkende layout file met:
- Proper metadata
- Tailwind CSS import
- Font configuratie
- Mobile viewport
- Nederlandse title/description

Werk systematisch door alle 5 files heen zodat `npm run dev` zonder errors start.
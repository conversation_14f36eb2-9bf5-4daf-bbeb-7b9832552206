// src/components/mobile/CameraCapture.tsx
/**
 * 📸 CAMERA CAPTURE COMPONENT
 * 
 * Mobile camera component voor het maken en uploaden van foto's.
 * Ondersteunt zowel camera als galerij selectie.
 */

'use client';

import React, { useState, useRef, useCallback, useMemo } from 'react';
import { CameraConfig, ProjectFoto } from '@/types';

interface CameraCaptureProps {
  projectId: string;
  onPhotoUploaded: (photo: ProjectFoto) => void;
  onError: (error: string) => void;
  maxPhotos?: number;
  config?: Partial<CameraConfig>;
}

interface CameraState {
  isCapturing: boolean;
  isUploading: boolean;
  error: string | null;
  photos: ProjectFoto[];
}

const DEFAULT_CONFIG: CameraConfig = {
  quality: 0.8,
  maxWidth: 1920,
  maxHeight: 1080,
  format: 'jpeg'
};

export default function CameraCapture({
  projectId,
  onPhotoUploaded,
  onError,
  maxPhotos = 10,
  config = {}
}: CameraCaptureProps) {
  const [state, setState] = useState<CameraState>({
    isCapturing: false,
    isUploading: false,
    error: null,
    photos: []
  });

  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  const finalConfig = useMemo(() => ({ ...DEFAULT_CONFIG, ...config }), [config]);

  // ===== CAMERA INITIALISATIE =====
  const startCamera = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, error: null, isCapturing: true }));

      const constraints = {
        video: {
          width: { ideal: finalConfig.maxWidth },
          height: { ideal: finalConfig.maxHeight },
          facingMode: 'environment' // Achtercamera
        },
        audio: false
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        await videoRef.current.play();
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Camera kon niet gestart worden';
      setState(prev => ({ ...prev, error: errorMessage, isCapturing: false }));
      onError(errorMessage);
    }
  }, [finalConfig, onError]);

  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setState(prev => ({ ...prev, isCapturing: false }));
  }, []);

  // ===== FOTO UPLOAD =====
  const uploadPhoto = useCallback(async (blob: Blob, filename: string) => {
    try {
      // Maak FormData voor upload
      const formData = new FormData();
      formData.append('file', blob, filename);
      formData.append('projectId', projectId);

      // Upload naar API
      const response = await fetch('/api/photos/upload', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`Upload mislukt: ${response.statusText}`);
      }

      const result = await response.json();

      if (!result.success) {
        throw new Error(result.error || 'Upload mislukt');
      }

      const photo: ProjectFoto = {
        id: result.data.id,
        filename: result.data.filename,
        url: result.data.url,
        createdAt: new Date(result.data.createdAt)
      };

      setState(prev => ({
        ...prev,
        photos: [...prev.photos, photo]
      }));

      onPhotoUploaded(photo);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload mislukt';
      throw new Error(errorMessage);
    }
  }, [projectId, onPhotoUploaded]);

  // ===== FOTO CAPTURE =====
  const capturePhoto = useCallback(async () => {
    if (!videoRef.current || !canvasRef.current || state.isUploading) return;

    try {
      setState(prev => ({ ...prev, isUploading: true }));

      const video = videoRef.current;
      const canvas = canvasRef.current;
      const ctx = canvas.getContext('2d');

      if (!ctx) throw new Error('Canvas context niet beschikbaar');

      // Stel canvas afmetingen in
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Teken video frame naar canvas
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Converteer naar blob
      const blob = await new Promise<Blob>((resolve) => {
        canvas.toBlob(
          (blob) => resolve(blob!),
          `image/${finalConfig.format}`,
          finalConfig.quality
        );
      });

      // Maak bestandsnaam
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `vloerwerk-${projectId}-${timestamp}.${finalConfig.format}`;

      // Upload foto
      await uploadPhoto(blob, filename);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Foto kon niet gemaakt worden';
      setState(prev => ({ ...prev, error: errorMessage }));
      onError(errorMessage);
    } finally {
      setState(prev => ({ ...prev, isUploading: false }));
    }
  }, [projectId, finalConfig, state.isUploading, onError, uploadPhoto]);



  // ===== GALERIJ SELECTIE =====
  const selectFromGallery = useCallback(async () => {
    try {
      const input = document.createElement('input');
      input.type = 'file';
      input.accept = 'image/*';
      input.multiple = false;

      input.onchange = async (event) => {
        const file = (event.target as HTMLInputElement).files?.[0];
        if (!file) return;

        // Controleer bestandsgrootte (max 10MB)
        if (file.size > 10 * 1024 * 1024) {
          onError('Bestand is te groot (max 10MB)');
          return;
        }

        // Controleer bestandsformaat
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
          onError('Ongeldig bestandsformaat');
          return;
        }

        setState(prev => ({ ...prev, isUploading: true }));

        try {
          await uploadPhoto(file, file.name);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Upload mislukt';
          setState(prev => ({ ...prev, error: errorMessage }));
          onError(errorMessage);
        } finally {
          setState(prev => ({ ...prev, isUploading: false }));
        }
      };

      input.click();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Galerij selectie mislukt';
      setState(prev => ({ ...prev, error: errorMessage }));
      onError(errorMessage);
    }
  }, [onError, uploadPhoto]);

  // ===== COMPONENT LIFECYCLE =====
  React.useEffect(() => {
    startCamera();
    return () => stopCamera();
  }, [startCamera, stopCamera]);

  // ===== RENDER =====
  if (state.photos.length >= maxPhotos) {
    return (
      <div className="p-4 text-center">
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-800 font-medium">
            Maximum aantal foto's bereikt ({maxPhotos})
          </p>
          <p className="text-yellow-600 text-sm mt-1">
            Verwijder enkele foto's om nieuwe toe te voegen
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative bg-black rounded-lg overflow-hidden">
      {/* Camera Preview */}
      <div className="relative">
        <video
          ref={videoRef}
          autoPlay
          playsInline
          muted
          className="w-full h-64 object-cover"
        />
        
        {/* Camera Overlay */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="border-2 border-white border-dashed rounded-lg p-8 opacity-50">
            <div className="text-white text-center">
              <div className="text-4xl mb-2">📸</div>
              <p className="text-sm">Richt camera op de vloer</p>
            </div>
          </div>
        </div>

        {/* Error Display */}
        {state.error && (
          <div className="absolute top-2 left-2 right-2 bg-red-500 text-white p-2 rounded text-sm">
            {state.error}
          </div>
        )}
      </div>

      {/* Hidden Canvas voor foto capture */}
      <canvas ref={canvasRef} className="hidden" />

      {/* Camera Controls */}
      <div className="p-4 bg-gray-900">
        <div className="flex items-center justify-center space-x-4">
          {/* Galerij Button */}
          <button
            onClick={selectFromGallery}
            disabled={state.isUploading}
            className="flex items-center space-x-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-500 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <span className="text-lg">📁</span>
            <span className="text-sm">Galerij</span>
          </button>

          {/* Capture Button */}
          <button
            onClick={capturePhoto}
            disabled={!state.isCapturing || state.isUploading}
            className="flex items-center justify-center w-16 h-16 bg-white rounded-full border-4 border-gray-300 hover:border-gray-400 disabled:opacity-50 transition-all"
          >
            {state.isUploading ? (
              <div className="w-6 h-6 border-2 border-gray-400 border-t-transparent rounded-full animate-spin" />
            ) : (
              <div className="w-8 h-8 bg-gray-800 rounded-full" />
            )}
          </button>

          {/* Camera Toggle */}
          <button
            onClick={state.isCapturing ? stopCamera : startCamera}
            disabled={state.isUploading}
            className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-500 text-white px-4 py-2 rounded-lg transition-colors"
          >
            <span className="text-lg">
              {state.isCapturing ? '⏸️' : '▶️'}
            </span>
            <span className="text-sm">
              {state.isCapturing ? 'Stop' : 'Start'}
            </span>
          </button>
        </div>

        {/* Status Info */}
        <div className="mt-3 text-center text-gray-400 text-sm">
          <p>
            Foto's: {state.photos.length}/{maxPhotos}
          </p>
          {state.isUploading && (
            <p className="text-blue-400">Foto uploaden...</p>
          )}
        </div>
      </div>
    </div>
  );
}
{"name": "vloerwerk-crm", "version": "1.0.0", "description": "AI-powered CRM voor vloerwerk bedrijven", "private": true, "scripts": {"dev": "next dev", "dev:mobile": "next dev", "build": "next build", "build:mobile": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:migrate": "prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:mobile": "jest --testPathPattern=mobile", "clean": "rm -rf .next out"}, "dependencies": {"@hookform/resolvers": "^3.0.0", "@prisma/client": "^6.12.0", "@radix-ui/react-dialog": "^1.0.0", "@radix-ui/react-dropdown-menu": "^2.0.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.0", "@radix-ui/react-toast": "^1.0.0", "@tailwindcss/forms": "^0.5.0", "axios": "^1.6.0", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "date-fns": "^2.30.0", "html2canvas": "^1.4.0", "jspdf": "^2.5.0", "lucide-react": "^0.300.0", "next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "react-hook-form": "^7.0.0", "recharts": "^2.8.0", "swr": "^2.2.0", "tailwind-merge": "^2.0.0", "zod": "^3.22.0"}, "devDependencies": {"@types/jest": "^29.0.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "jest": "^29.0.0", "jest-environment-jsdom": "^29.0.0", "postcss": "^8.4.0", "prettier": "^3.0.0", "prisma": "^6.12.0", "tailwindcss": "^3.3.0", "tsx": "^4.0.0", "typescript": "^5.3.0"}}
# 🏗️ Vloerwerk CRM

AI-powered CRM applicatie voor vloerwerk bedrijven met mobile PWA en desktop interface.

## 🚀 Features

- 📱 **Mobile PWA** voor veldwerk
- 💻 **Desktop CRM** voor administratie  
- 🤖 **AI-analyse** van vloer foto's
- 💰 **Automatische prijsberekening**
- 📊 **Complete project management**

## 🛠️ Tech Stack

- **Frontend**: Next.js 14, TypeScript, Tailwind CSS
- **Database**: Prisma + SQLite/PostgreSQL
- **AI**: TensorFlow.js
- **Mobile**: PWA met camera access
- **Forms**: React Hook Form + Zod

## 📦 Installation

```bash
# Install dependencies
npm install

# Setup database
npx prisma generate
npx prisma db push

# Start development
npm run dev
```

## 📱 Usage

### Mobile (Veldwerk)
1. Maak foto's van vloer
2. Voer afmetingen in
3. AI analyseert automatisch
4. Krijg directe prijsschatting

### Desktop (Kantoor)
1. Beheer klanten database
2. Bekijk project overzichten
3. Genereer offertes
4. Maak facturen

## 🏗️ Project Structure

```
src/
├── app/                 # Next.js pages
├── components/          # React components
├── lib/                 # Services & utilities
├── types/              # TypeScript types
└── hooks/              # Custom hooks
```

## 📄 License

Private - Vloerwerk CRM

<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App - Vloerwerk CRM</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .project-card {
            transition: all 0.2s ease;
        }
        .project-card:active {
            transform: scale(0.98);
            background-color: #f9fafb;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm border-b">
        <div class="px-4 py-3">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-lg font-semibold text-gray-900">
                        Vloerwerk CRM
                    </h1>
                    <p class="text-sm text-gray-600">
                        Mobile App
                    </p>
                </div>
                <div class="flex items-center space-x-2">
                    <div class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">
                        Online
                    </div>
                    <button onclick="openSettings()" class="text-gray-500 hover:text-gray-700">
                        ⚙️
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="p-4 bg-white border-b">
        <div class="grid grid-cols-2 gap-3">
            <button onclick="newProject()" class="bg-blue-500 text-white p-4 rounded-lg text-center">
                <div class="text-2xl mb-2">📋</div>
                <div class="text-sm font-medium">Nieuw Project</div>
            </button>
            
            <button onclick="openCamera()" class="bg-green-500 text-white p-4 rounded-lg text-center">
                <div class="text-2xl mb-2">📸</div>
                <div class="text-sm font-medium">Camera</div>
            </button>
        </div>
    </div>

    <!-- Projects List -->
    <div class="p-4">
        <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold text-gray-900">
                Projecten (<span id="project-count">3</span>)
            </h2>
            <button onclick="refreshProjects()" class="text-blue-500 text-sm">
                Ververs
            </button>
        </div>

        <div id="projects-list" class="space-y-3">
            <!-- Project 1 -->
            <div class="project-card bg-white rounded-lg p-4 shadow-sm border border-gray-200" onclick="openProject('1')">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900">
                            Woonkamer Renovatie
                        </h3>
                        <p class="text-sm text-gray-600">
                            Familie van der Berg
                        </p>
                        <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                            <span>📸 5 foto's</span>
                            <span>💰 €1.250</span>
                            <span>📅 Vandaag</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Actief
                        </span>
                        <div class="text-gray-400">→</div>
                    </div>
                </div>
            </div>

            <!-- Project 2 -->
            <div class="project-card bg-white rounded-lg p-4 shadow-sm border border-gray-200" onclick="openProject('2')">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900">
                            Kantoor Laminaat
                        </h3>
                        <p class="text-sm text-gray-600">
                            Bedrijf ABC
                        </p>
                        <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                            <span>📸 8 foto's</span>
                            <span>💰 €2.100</span>
                            <span>📅 Gisteren</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Voltooid
                        </span>
                        <div class="text-gray-400">→</div>
                    </div>
                </div>
            </div>

            <!-- Project 3 -->
            <div class="project-card bg-white rounded-lg p-4 shadow-sm border border-gray-200" onclick="openProject('3')">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h3 class="text-lg font-medium text-gray-900">
                            Slaapkamer Parket
                        </h3>
                        <p class="text-sm text-gray-600">
                            Mevrouw Jansen
                        </p>
                        <div class="flex items-center space-x-4 mt-2 text-sm text-gray-500">
                            <span>📸 3 foto's</span>
                            <span>💰 €850</span>
                            <span>📅 2 dagen geleden</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-2">
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Concept
                        </span>
                        <div class="text-gray-400">→</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center p-2 text-blue-500">
                <div class="text-xl">🏠</div>
                <span class="text-xs">Home</span>
            </button>
            
            <button onclick="openCamera()" class="flex flex-col items-center p-2 text-gray-500">
                <div class="text-xl">📸</div>
                <span class="text-xs">Camera</span>
            </button>
            
            <button onclick="newProject()" class="flex flex-col items-center p-2 text-gray-500">
                <div class="text-xl">➕</div>
                <span class="text-xs">Nieuw</span>
            </button>
            
            <button onclick="openSettings()" class="flex flex-col items-center p-2 text-gray-500">
                <div class="text-xl">⚙️</div>
                <span class="text-xs">Instellingen</span>
            </button>
        </div>
    </div>

    <!-- Floating Action Button -->
    <button onclick="newProject()" class="fixed bottom-20 right-4 bg-blue-500 text-white w-14 h-14 rounded-full shadow-lg flex items-center justify-center text-2xl">
        ➕
    </button>

    <script>
        function openCamera() {
            window.location.href = 'camera.html';
        }

        function newProject() {
            alert('🚧 Nieuw Project functionaliteit komt binnenkort!\n\nHier kun je:\n• Klantgegevens invoeren\n• Project naam instellen\n• Afmetingen opgeven\n• Direct foto\'s maken');
        }

        function openProject(id) {
            alert(`🔍 Project ${id} Details\n\nHier zie je:\n• Alle project foto's\n• AI analyse resultaten\n• Materiaal detectie\n• Prijsberekening\n• Status updates`);
        }

        function openSettings() {
            alert('⚙️ Instellingen\n\nHier kun je:\n• Account beheren\n• Offline instellingen\n• Camera kwaliteit\n• Sync opties\n• App voorkeuren');
        }

        function refreshProjects() {
            const button = event.target;
            button.textContent = 'Laden...';
            
            setTimeout(() => {
                button.textContent = 'Ververs';
                showNotification('✅ Projecten bijgewerkt!');
            }, 1000);
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 left-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg text-center z-50';
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Simulate real-time updates
        setInterval(() => {
            const onlineStatus = document.querySelector('.bg-green-100');
            if (Math.random() > 0.9) {
                onlineStatus.className = 'bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs';
                onlineStatus.textContent = 'Sync...';
                
                setTimeout(() => {
                    onlineStatus.className = 'bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs';
                    onlineStatus.textContent = 'Online';
                }, 2000);
            }
        }, 10000);
    </script>
</body>
</html>

// prisma/seed.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Maak test projecten aan
  const project1 = await prisma.project.create({
    data: {
      name: 'Woonkamer Renovatie',
      clientName: '<PERSON>',
      clientEmail: '<EMAIL>',
      status: 'NIEUW'
    }
  });

  const project2 = await prisma.project.create({
    data: {
      name: '<PERSON><PERSON><PERSON>',
      clientName: '<PERSON>',
      clientEmail: '<EMAIL>',
      status: 'AKTIEF'
    }
  });

  const project3 = await prisma.project.create({
    data: {
      name: '<PERSON><PERSON><PERSON>',
      clientName: 'Bedrijf BV',
      clientEmail: '<EMAIL>',
      status: 'VOLTOOID'
    }
  });

  // Voeg foto's toe
  await prisma.photo.create({
    data: {
      filename: 'woonkamer-1.jpg',
      url: '/uploads/woonkamer-1.jpg',
      projectId: project1.id
    }
  });

  await prisma.photo.create({
    data: {
      filename: 'keuken-1.jpg', 
      url: '/uploads/keuken-1.jpg',
      projectId: project2.id
    }
  });

  // Voeg analyses toe
  await prisma.analysis.create({
    data: {
      material: 'LAMINAAT',
      condition: 'goed',
      priceEstimate: 850.50,
      confidence: 0.85,
      projectId: project1.id
    }
  });

  await prisma.analysis.create({
    data: {
      material: 'TEGELS',
      condition: 'redelijk', 
      priceEstimate: 1200.00,
      confidence: 0.92,
      projectId: project2.id
    }
  });

  await prisma.analysis.create({
    data: {
      material: 'PARKET',
      condition: 'uitstekend',
      priceEstimate: 2500.00,
      confidence: 0.95,
      projectId: project3.id
    }
  });

  // Voeg metingen toe
  await prisma.measurement.create({
    data: {
      length: 5.2,
      width: 4.1,
      height: 2.8,
      projectId: project1.id
    }
  });

  await prisma.measurement.create({
    data: {
      length: 3.8,
      width: 2.9,
      height: 2.5,
      projectId: project2.id
    }
  });

  await prisma.measurement.create({
    data: {
      length: 8.5,
      width: 6.2,
      height: 3.0,
      projectId: project3.id
    }
  });

  console.log('✅ Database seeded successfully!');
  console.log(`Created ${await prisma.project.count()} projects`);
  console.log(`Created ${await prisma.photo.count()} photos`);
  console.log(`Created ${await prisma.analysis.count()} analyses`);
  console.log(`Created ${await prisma.measurement.count()} measurements`);
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });

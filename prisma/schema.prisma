generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  clientName  String
  clientEmail String?
  status      String   @default("NIEUW")
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  photos      Photo[]
  measurements Measurement[]
  analysis    Analysis[]
}

model Photo {
  id        String   @id @default(cuid())
  filename  String
  url       String
  projectId String
  project   Project  @relation(fields: [projectId], references: [id])
  createdAt DateTime @default(now())
}

model Measurement {
  id        String  @id @default(cuid())
  length    Float
  width     Float
  height    Float?
  projectId String
  project   Project @relation(fields: [projectId], references: [id])
}

model Analysis {
  id           String  @id @default(cuid())
  material     String
  condition    String
  priceEstimate Float
  confidence   Float
  projectId    String
  project      Project @relation(fields: [projectId], references: [id])
  createdAt    DateTime @default(now())
}

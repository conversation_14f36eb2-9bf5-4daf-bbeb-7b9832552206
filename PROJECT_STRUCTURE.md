# 🏗️ VLOERWERK CRM - PROJECT STRUCTUUR

## 📁 OVERZICHT

```
Calculator/
├── 📱 FRONTEND (Next.js 14)
│   ├── src/
│   │   ├── app/                    # Next.js App Router
│   │   │   ├── (auth)/             # Authenticatie routes
│   │   │   ├── (dashboard)/        # Dashboard routes
│   │   │   ├── api/                # API routes (Backend)
│   │   │   ├── mobile/             # Mobile PWA routes
│   │   │   ├── desktop/            # Desktop CRM routes
│   │   │   └── globals.css
│   │   ├── components/             # React Components
│   │   │   ├── ui/                 # UI Components (shadcn/ui)
│   │   │   ├── forms/              # Form Components
│   │   │   ├── mobile/             # Mobile Components
│   │   │   ├── desktop/            # Desktop Components
│   │   │   └── shared/             # Shared Components
│   │   ├── lib/                    # Utilities & Services
│   │   │   ├── api/                # API Client
│   │   │   ├── auth/               # Authenticatie
│   │   │   ├── db/                 # Database Services
│   │   │   ├── ai/                 # AI Services
│   │   │   └── utils/              # Utilities
│   │   ├── hooks/                  # Custom React Hooks
│   │   ├── types/                  # TypeScript Types
│   │   └── styles/                 # Styling
│   └── public/                     # Static Assets
│
├── 🔧 BACKEND (API Routes)
│   ├── src/app/api/
│   │   ├── auth/                   # Authenticatie API
│   │   ├── projects/               # Project Management API
│   │   ├── photos/                 # Foto Upload API
│   │   ├── ai/                     # AI Analyse API
│   │   ├── analytics/              # Analytics API
│   │   └── users/                  # User Management API
│   └── src/lib/
│       ├── db/                     # Database Services
│       ├── ai/                     # AI Services
│       ├── auth/                   # Auth Services
│       └── utils/                  # Backend Utilities
│
├── 🗄️ DATABASE
│   ├── prisma/
│   │   ├── schema.prisma          # Database Schema
│   │   ├── seed.ts                # Database Seeding
│   │   └── migrations/            # Database Migrations
│   └── dev.db                     # SQLite Database
│
└── 📋 CONFIGURATIE
    ├── .env.local                 # Environment Variables
    ├── tailwind.config.js         # Tailwind Config
    ├── next.config.js             # Next.js Config
    └── tsconfig.json              # TypeScript Config
```

## 🎯 FRONTEND STRUCTUUR

### 📱 Mobile PWA Interface
```
src/app/mobile/
├── page.tsx                       # Mobile Home
├── project/
│   ├── [id]/page.tsx             # Project Details
│   └── new/page.tsx              # Nieuw Project
├── camera/
│   └── page.tsx                  # Camera Interface
└── settings/
    └── page.tsx                  # Mobile Settings
```

### 💻 Desktop CRM Interface
```
src/app/desktop/
├── page.tsx                       # Dashboard
├── projects/
│   ├── page.tsx                  # Project Overzicht
│   ├── [id]/page.tsx            # Project Details
│   └── new/page.tsx             # Nieuw Project
├── clients/
│   ├── page.tsx                  # Klant Overzicht
│   └── [id]/page.tsx            # Klant Details
├── analytics/
│   └── page.tsx                  # Analytics Dashboard
└── settings/
    └── page.tsx                  # CRM Settings
```

### 🔐 Authenticatie
```
src/app/(auth)/
├── login/page.tsx                # Login Pagina
├── register/page.tsx             # Registratie
└── forgot-password/page.tsx      # Wachtwoord Reset
```

## 🔧 BACKEND STRUCTUUR

### 📡 API Routes
```
src/app/api/
├── auth/
│   ├── login/route.ts            # Login API
│   ├── register/route.ts         # Registratie API
│   └── logout/route.ts           # Logout API
├── projects/
│   ├── route.ts                  # CRUD Projecten
│   ├── [id]/route.ts            # Project Details
│   └── [id]/analysis/route.ts   # Project Analyse
├── photos/
│   ├── upload/route.ts           # Foto Upload
│   └── [id]/route.ts            # Foto Management
├── ai/
│   ├── analyze/route.ts          # AI Analyse
│   └── models/route.ts           # AI Models
├── analytics/
│   ├── stats/route.ts            # Statistieken
│   └── reports/route.ts          # Rapporten
└── users/
    ├── route.ts                  # User Management
    └── [id]/route.ts            # User Details
```

### 🗄️ Database Services
```
src/lib/db/
├── index.ts                      # Database Client
├── projects.ts                   # Project Services
├── photos.ts                     # Photo Services
├── users.ts                      # User Services
├── analytics.ts                  # Analytics Services
└── migrations.ts                 # Migration Helpers
```

### 🤖 AI Services
```
src/lib/ai/
├── index.ts                      # AI Service Client
├── vision.ts                     # Computer Vision
├── pricing.ts                    # Prijsberekening
├── analysis.ts                   # Analyse Services
└── models.ts                     # AI Models
```

## 🎨 COMPONENT STRUCTUUR

### 🧩 UI Components
```
src/components/ui/
├── Button.tsx                    # Button Component
├── Input.tsx                     # Input Component
├── Modal.tsx                     # Modal Component
├── Card.tsx                      # Card Component
├── Table.tsx                     # Table Component
└── index.ts                      # UI Exports
```

### 📱 Mobile Components
```
src/components/mobile/
├── CameraCapture.tsx             # Camera Component
├── PhotoGallery.tsx              # Foto Gallery
├── ProjectCard.tsx               # Project Card
├── Navigation.tsx                # Mobile Nav
└── OfflineIndicator.tsx          # Offline Status
```

### 💻 Desktop Components
```
src/components/desktop/
├── Dashboard.tsx                 # Dashboard
├── ProjectTable.tsx              # Project Table
├── Analytics.tsx                 # Analytics
├── Sidebar.tsx                   # Sidebar
└── Header.tsx                    # Header
```

### 🔄 Shared Components
```
src/components/shared/
├── DeviceDetection.tsx           # Device Detection
├── ErrorBoundary.tsx             # Error Boundary
├── LoadingSpinner.tsx            # Loading
├── Toast.tsx                     # Notifications
└── Layout.tsx                    # Layout Wrapper
```

## 🎣 CUSTOM HOOKS

```
src/hooks/
├── useAuth.ts                    # Authenticatie Hook
├── useProjects.ts                # Project Management
├── usePhotos.ts                  # Foto Management
├── useAI.ts                      # AI Services
├── useAnalytics.ts               # Analytics
└── useDevice.ts                  # Device Detection
```

## 📊 TYPES

```
src/types/
├── index.ts                      # Main Types
├── auth.ts                       # Auth Types
├── projects.ts                   # Project Types
├── photos.ts                     # Photo Types
├── ai.ts                         # AI Types
├── api.ts                        # API Types
└── ui.ts                         # UI Types
```

## 🚀 DEVELOPMENT WORKFLOW

### 1. Frontend Development
```bash
# Start development server
npm run dev

# Build voor productie
npm run build

# Type checking
npm run type-check
```

### 2. Backend Development
```bash
# Database migraties
npx prisma migrate dev

# Database seeding
npm run db:seed

# API testing
npm run test:api
```

### 3. Mobile PWA
```bash
# PWA build
npm run build:pwa

# Service worker
npm run sw:generate
```

## 🔐 SECURITY & AUTHENTICATIE

### User Roles
- **Admin**: Volledige toegang
- **ProjectManager**: Project management + AI
- **Employee**: Toegewezen projecten
- **DemoUser**: Alleen lezen

### API Security
- JWT tokens
- Role-based access
- Rate limiting
- Input validation

## 📱 PWA FEATURES

### Mobile PWA
- Camera access
- Offline functionality
- Push notifications
- Background sync
- Install prompt

### Desktop CRM
- Real-time updates
- Advanced analytics
- Export functionality
- Multi-user support

## 🎯 BUSINESS LOGIC

### Project Flow
1. **NIEUW** → Project aangemaakt
2. **AKTIEF** → Foto's toegevoegd
3. **VOLTOOID** → AI analyse gedaan
4. **GEFACTUREERD** → Factuur gemaakt

### AI Analysis
- Materiaal detectie
- Conditie beoordeling
- Prijsberekening
- Tijdschatting

### Pricing Logic
- Materiaalkosten
- Arbeidskosten
- Complexiteitstoeslag
- BTW berekening 
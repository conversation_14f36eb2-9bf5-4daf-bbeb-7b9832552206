<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vloerwerk CRM - Calculator App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
    <div class="max-w-4xl mx-auto px-4 py-16">
        <div class="text-center">
            <!-- Logo en Titel -->
            <div class="mb-8">
                <div class="text-6xl mb-4">🏠</div>
                <h1 class="text-4xl font-bold text-gray-900 mb-4">
                    Vloerwerk CRM
                </h1>
                <p class="text-xl text-gray-600 mb-8">
                    AI-powered CRM voor vloerwerk bedrijven
                </p>
            </div>

            <!-- Features -->
            <div class="grid md:grid-cols-3 gap-8 mb-12">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-3xl mb-4">📸</div>
                    <h3 class="text-lg font-semibold mb-2">Camera Capture</h3>
                    <p class="text-gray-600">
                        Maak foto's van vloeren met je smartphone camera
                    </p>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-3xl mb-4">🤖</div>
                    <h3 class="text-lg font-semibold mb-2">AI Analyse</h3>
                    <p class="text-gray-600">
                        Automatische materiaal detectie en prijsberekening
                    </p>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="text-3xl mb-4">📱</div>
                    <h3 class="text-lg font-semibold mb-2">Offline Werken</h3>
                    <p class="text-gray-600">
                        Werk zonder internet, synchroniseer later
                    </p>
                </div>
            </div>

            <!-- Calculator Demo -->
            <div class="bg-white rounded-lg p-8 shadow-sm mb-8">
                <h2 class="text-2xl font-bold mb-6">Vloer Calculator</h2>
                <div class="max-w-md mx-auto">
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Lengte (m)</label>
                            <input type="number" id="length" class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="5.0">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Breedte (m)</label>
                            <input type="number" id="width" class="w-full px-3 py-2 border border-gray-300 rounded-md" placeholder="4.0">
                        </div>
                    </div>
                    
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Materiaal Type</label>
                        <select id="material" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            <option value="laminaat">Laminaat (€15/m²)</option>
                            <option value="parket">Parket (€35/m²)</option>
                            <option value="vinyl">Vinyl (€20/m²)</option>
                            <option value="tegels">Tegels (€25/m²)</option>
                        </select>
                    </div>

                    <button onclick="calculatePrice()" class="w-full bg-blue-500 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-600 transition-colors">
                        Bereken Prijs
                    </button>

                    <div id="result" class="mt-6 p-4 bg-gray-50 rounded-lg hidden">
                        <div class="text-lg font-semibold text-gray-900" id="total-area"></div>
                        <div class="text-xl font-bold text-blue-600" id="total-price"></div>
                    </div>
                </div>
            </div>

            <!-- App Info -->
            <div class="bg-white rounded-lg p-8 shadow-sm">
                <h2 class="text-2xl font-bold mb-6">Volledige App Features</h2>
                <div class="grid md:grid-cols-2 gap-6 text-left">
                    <div>
                        <h3 class="font-semibold text-lg mb-3">📱 Mobile App</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li>• Camera functionaliteit</li>
                            <li>• Offline werken</li>
                            <li>• Touch-friendly interface</li>
                            <li>• PWA installatie</li>
                        </ul>
                    </div>
                    <div>
                        <h3 class="font-semibold text-lg mb-3">🖥️ Desktop CRM</h3>
                        <ul class="space-y-2 text-gray-600">
                            <li>• Project management</li>
                            <li>• Klanten database</li>
                            <li>• Facturatie systeem</li>
                            <li>• Analytics dashboard</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Development Status -->
            <div class="mt-12 text-center text-gray-500">
                <p class="text-sm">
                    🚧 Development versie - Next.js server wordt geladen...
                </p>
            </div>
        </div>
    </div>

    <script>
        function calculatePrice() {
            const length = parseFloat(document.getElementById('length').value) || 0;
            const width = parseFloat(document.getElementById('width').value) || 0;
            const material = document.getElementById('material').value;
            
            const prices = {
                'laminaat': 15,
                'parket': 35,
                'vinyl': 20,
                'tegels': 25
            };
            
            const area = length * width;
            const pricePerM2 = prices[material];
            const totalPrice = area * pricePerM2;
            
            if (area > 0) {
                document.getElementById('total-area').textContent = `Oppervlakte: ${area.toFixed(2)} m²`;
                document.getElementById('total-price').textContent = `Totaal: €${totalPrice.toFixed(2)}`;
                document.getElementById('result').classList.remove('hidden');
            }
        }
        
        // Auto-calculate when inputs change
        document.getElementById('length').addEventListener('input', calculatePrice);
        document.getElementById('width').addEventListener('input', calculatePrice);
        document.getElementById('material').addEventListener('change', calculatePrice);
    </script>
</body>
</html>

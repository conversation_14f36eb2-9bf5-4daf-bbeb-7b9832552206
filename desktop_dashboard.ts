// src/app/desktop/components/Dashboard.tsx
'use client';

import { useState, useEffect } from 'react';
import { 
  Users, 
  FolderOpen, 
  FileText, 
  TrendingUp, 
  Calendar,
  Euro,
  Camera,
  Clock
} from 'lucide-react';
import { VloerProject, ProjectStatus } from '@/types';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';

interface DashboardStats {
  totalProjects: number;
  activeProjects: number;
  completedProjects: number;
  totalRevenue: number;
  monthlyRevenue: number;
  pendingInvoices: number;
  avgProjectValue: number;
}

interface MonthlyData {
  month: string;
  projects: number;
  revenue: number;
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats>({
    totalProjects: 0,
    activeProjects: 0,
    completedProjects: 0,
    totalRevenue: 0,
    monthlyRevenue: 0,
    pendingInvoices: 0,
    avgProjectValue: 0
  });
  
  const [recentProjects, setRecentProjects] = useState<VloerProject[]>([]);
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Laad statistieken
      const statsResponse = await fetch('/api/dashboard/stats');
      const statsData = await statsResponse.json();
      setStats(statsData);
      
      // Laad recente projecten
      const projectsResponse = await fetch('/api/projecten?limit=5&orderBy=createdAt');
      const projectsData = await projectsResponse.json();
      setRecentProjects(projectsData.items);
      
      // Laad maandelijkse data voor charts
      const chartResponse = await fetch('/api/dashboard/charts');
      const chartData = await chartResponse.json();
      setMonthlyData(chartData.monthly);
      
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const StatCard = ({ 
    icon: Icon, 
    title, 
    value, 
    subtitle, 
    trend, 
    color = 'blue' 
  }: {
    icon: any;
    title: string;
    value: string | number;
    subtitle?: string;
    trend?: string;
    color?: 'blue' | 'green' | 'purple' | 'orange';
  }) => {
    const colorClasses = {
      blue: 'bg-blue-500 text-blue-100',
      green: 'bg-green-500 text-green-100',
      purple: 'bg-purple-500 text-purple-100',
      orange: 'bg-orange-500 text-orange-100'
    };

    return (
      <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-gray-600 text-sm font-medium">{title}</p>
            <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
            {subtitle && (
              <p className="text-gray-500 text-sm mt-1">{subtitle}</p>
            )}
          </div>
          <div className={`p-3 rounded-full ${colorClasses[color]}`}>
            <Icon size={24} />
          </div>
        </div>
        {trend && (
          <div className="mt-4 flex items-center">
            <TrendingUp size={16} className="text-green-500 mr-1" />
            <span className="text-green-600 text-sm font-medium">{trend}</span>
          </div>
        )}
      </div>
    );
  };

  const ProjectStatusBadge = ({ status }: { status: ProjectStatus }) => {
    const statusConfig = {
      [ProjectStatus.AKTIEF]: { color: 'bg-blue-100 text-blue-800', text: 'Actief' },
      [ProjectStatus.AFGEROND]: { color: 'bg-green-100 text-green-800', text: 'Afgerond' },
      [ProjectStatus.GEANNULEERD]: { color: 'bg-red-100 text-red-800', text: 'Geannuleerd' },
      [ProjectStatus.WACHT_OP_KLANT]: { color: 'bg-yellow-100 text-yellow-800', text: 'Wacht op klant' }
    };
    
    const config = statusConfig[status];
    
    return (
      <span className={`px-2 py-1 rounded-full text-xs font-medium ${config.color}`}>
        {config.text}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="p-8">
        <div className="animate-pulse space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="bg-gray-200 h-32 rounded-lg"></div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-gray-200 h-64 rounded-lg"></div>
            <div className="bg-gray-200 h-64 rounded-lg"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Dashboard</h1>
          <p className="text-gray-600 mt-1">Overzicht van je vloerwerk projecten</p>
        </div>
        <button 
          onClick={() => window.open('/mobile', '_blank')}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700 transition-colors"
        >
          <Camera size={20} />
          Open Mobile App
        </button>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          icon={FolderOpen}
          title="Totaal Projecten"
          value={stats.totalProjects}
          subtitle={`${stats.activeProjects} actief`}
          color="blue"
        />
        <StatCard
          icon={Euro}
          title="Maand Omzet"
          value={`€${stats.monthlyRevenue.toLocaleString()}`}
          trend="+12% vs vorige maand"
          color="green"
        />
        <StatCard
          icon={FileText}
          title="Open Facturen"
          value={stats.pendingInvoices}
          subtitle="Wacht op betaling"
          color="orange"
        />
        <StatCard
          icon={TrendingUp}
          title="Gem. Project Waarde"
          value={`€${stats.avgProjectValue.toLocaleString()}`}
          color="purple"
        />
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Chart */}
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Maandelijkse Omzet</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip formatter={(value) => [`€${value}`, 'Omzet']} />
                <Line 
                  type="monotone" 
                  dataKey="revenue" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                  dot={{ fill: '#3B82F6' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </div>

        {/* Projects Chart */}
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Projecten per Maand</h3>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={monthlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="projects" fill="#10B981" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>

      {/* Recent Projects */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-gray-900">Recente Projecten</h3>
            <button 
              onClick={() => window.location.href = '/desktop/crm/projecten'}
              className="text-blue-600 hover:text-blue-700 text-sm font-medium"
            >
              Alle projecten →
            </button>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Project
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Klant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Geschatte Prijs
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Datum
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Acties
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {recentProjects.map((project) => (
                <tr key={project.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <FolderOpen size={20} className="text-gray-600" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {project.naam}
                        </div>
                        <div className="text-sm text-gray-500">
                          {project.oppervlakte ? `${project.oppervlakte}m²` : 'Oppervlakte onbekend'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {project.klant?.naam || 'Onbekend'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <ProjectStatusBadge status={project.status} />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {project.geschattePrijs ? `€${project.geschattePrijs.toLocaleString()}` : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {project.createdAt ? new Date(project.createdAt).toLocaleDateString('nl-NL') : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button 
                      onClick={() => window.location.href = `/desktop/crm/projecten/${project.id}`}
                      className="text-blue-600 hover:text-blue-700 mr-4"
                    >
                      Bekijken
                    </button>
                    <button 
                      onClick={() => window.location.href = `/desktop/crm/facturen/new?projectId=${project.id}`}
                      className="text-green-600 hover:text-green-700"
                    >
                      Factureren
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {recentProjects.length === 0 && (
          <div className="p-8 text-center">
            <FolderOpen size={48} className="text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Nog geen projecten. Start je eerste project via de mobile app!</p>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <button 
          onClick={() => window.location.href = '/desktop/crm/klanten'}
          className="bg-white border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-blue-400 hover:bg-blue-50 transition-colors"
        >
          <Users size={32} className="text-gray-400 mx-auto mb-2" />
          <h4 className="text-lg font-medium text-gray-900">Klanten Beheren</h4>
          <p className="text-gray-600 text-sm mt-1">Voeg nieuwe klanten toe of bewerk bestaande</p>
        </button>
        
        <button 
          onClick={() => window.location.href = '/desktop/crm/facturen'}
          className="bg-white border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-green-400 hover:bg-green-50 transition-colors"
        >
          <FileText size={32} className="text-gray-400 mx-auto mb-2" />
          <h4 className="text-lg font-medium text-gray-900">Facturen</h4>
          <p className="text-gray-600 text-sm mt-1">Maak en verstuur facturen</p>
        </button>
        
        <button 
          onClick={() => window.location.href = '/desktop/crm/rapporten'}
          className="bg-white border-2 border-dashed border-gray-300 rounded-lg p-6 hover:border-purple-400 hover:bg-purple-50 transition-colors"
        >
          <TrendingUp size={32} className="text-gray-400 mx-auto mb-2" />
          <h4 className="text-lg font-medium text-gray-900">Rapportages</h4>
          <p className="text-gray-600 text-sm mt-1">Bekijk gedetailleerde analyses</p>
        </button>
      </div>
    </div>
  );
}
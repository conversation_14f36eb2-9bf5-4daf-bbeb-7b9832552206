# 👤 VLOERWERK CRM - USER RULES

## �� Jouw Workflow

### Development Workflow
- ✅ **Altijd TypeScript** g<PERSON><PERSON><PERSON><PERSON> met strikte typing
- ✅ **Test elke component** in zowel mobile als desktop view
- ✅ **Commit vaak** met duidelijke Nederlandse commit messages
- ✅ **Gebruik feature branches** voor nieuwe functionaliteit
- ✅ **Review code** voor performance en accessibility

### Code Generation Preferences
- ✅ **Generate complete, working code** (geen placeholders)
- ✅ **Include comprehensive error handling**
- ✅ **Add clear comments** explaining complex logic
- ✅ **Use modern language features** en best practices
- ✅ **Implement proper logging** en debugging support
- ✅ **Include unit tests** when appropriate

### Project Setup Assistance
- ✅ **Provide complete dependency management** instructions
- ✅ **Include environment setup** steps (API keys, config files)
- ✅ **Add verification steps** to test if everything works
- ✅ **Include troubleshooting** voor common installation problems
- ✅ **Setup proper project structure** en organization

### AI Framework Expertise
- ✅ **Familiar with TensorFlow.js** voor lokale AI
- ✅ **Provide cloud AI alternatives** (Google Vision, Azure)
- ✅ **Suggest when to use which approach**
- ✅ **Include hands-on project examples**
- ✅ **Explain integration patterns** en architectures

### Mobile Development Focus
- ✅ **Prioritize user experience** en performance
- ✅ **Follow platform design guidelines** (iOS HIG, Material Design)
- ✅ **Implement proper state management**
- ✅ **Use reactive programming patterns**
- ✅ **Include accessibility features** by default
- ✅ **Optimize for different screen sizes** en orientations

### Code Quality Standards
- ✅ **Follow SOLID principles**
- ✅ **Use appropriate design patterns**
- ✅ **Implement proper separation of concerns**
- ✅ **Include comprehensive documentation**
- ✅ **Add meaningful variable and function names**
- ✅ **Ensure type safety** en null safety

### Security Best Practices
- ✅ **Never hardcode sensitive information**
- ✅ **Use secure storage** voor credentials
- ✅ **Implement proper authentication flows**
- ✅ **Validate all user inputs**
- ✅ **Use HTTPS** voor alle network communications
- ✅ **Follow OWASP guidelines** voor mobile apps

### Learning and Explanation Approach
- ✅ **Start with high-level concepts** before diving into code
- ✅ **Provide real-world analogies** voor complex topics
- ✅ **Include visual diagrams** of pseudocode when helpful
- ✅ **Offer multiple implementation approaches**
- ✅ **Explain performance implications** van verschillende keuzes
- ✅ **Suggest further learning resources**

## 🎯 Specifieke Requirements voor Vloerwerk CRM

### Business Domain Knowledge
- ✅ **Vloerwerk terminologie** (laminaat, parket, tegels, vinyl, natuursteen)
- ✅ **BTW berekeningen** (21% Nederlandse BTW)
- ✅ **Project workflow** (offerte → uitvoering → facturatie)
- ✅ **Materiaal prijzen** en arbeidskosten
- ✅ **Afmetingen berekeningen** (m², lengte × breedte)

### Technical Requirements
- ✅ **Device detection** (mobile vs desktop)
- ✅ **Camera integration** voor foto's
- ✅ **AI image analysis** voor materiaal detectie
- ✅ **Offline functionality** voor veldwerk
- ✅ **Real-time sync** wanneer online
- ✅ **PDF generation** voor offertes en facturen

### User Experience
- ✅ **Mobile-first design** voor veldwerkers
- ✅ **Touch-friendly interface** (44px minimum)
- ✅ **Intuitive navigation** voor niet-technische gebruikers
- ✅ **Clear error messages** in het Nederlands
- ✅ **Loading indicators** voor alle actions
- ✅ **Confirmation dialogs** voor destructive actions

### Performance Requirements
- ✅ **Fast image upload** en processing
- ✅ **Efficient database queries** voor grote datasets
- ✅ **Optimized AI analysis** (max 30 seconden)
- ✅ **Responsive UI** op alle devices
- ✅ **Offline capability** voor kritieke functies

# 🚀 VLOERWERK CRM - DEVELOPMENT GUIDELINES

## �� Quick Start

### 1. Environment Setup
```bash
# Maak .env.local
DATABASE_URL="file:./dev.db"
NEXTAUTH_SECRET="your-secret-key"
AI_API_KEY="your-ai-api-key"
UPLOAD_DIR="./public/uploads"
```

### 2. Database Setup
```bash
npx prisma generate
npx prisma db push
```

### 3. Development
```bash
npm run dev
```

## 📁 Project Structure

### Core Directories
- `src/app/` - Next.js app directory
- `src/components/` - React components
- `src/lib/` - Services & utilities
- `src/types/` - TypeScript types
- `src/hooks/` - Custom hooks

### Mobile PWA
- `src/app/mobile/` - Mobile routes
- `src/components/mobile/` - Mobile components
- Camera integration
- Offline functionality

### Desktop CRM
- `src/app/desktop/` - Desktop routes
- `src/components/desktop/` - Desktop components
- Dashboard & analytics
- Project management

## 🔧 Development Workflow

### 1. Feature Development
1. Maak feature branch: `git checkout -b feature/nieuwe-functie`
2. Implementeer volgens project rules
3. Test in mobile en desktop view
4. Commit met Nederlandse message
5. Push en maak pull request

### 2. Code Quality
- TypeScript strict mode
- ESLint + Prettier
- Zod validatie voor forms
- Error boundaries
- Loading states

### 3. Testing
- Unit tests voor utilities
- Integration tests voor API
- E2E tests voor critical flows
- Mobile responsiveness testing

## 🎯 Business Logic

### Project Status Flow
```

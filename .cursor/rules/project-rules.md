# 🏗️ VLOERWERK CRM - PROJECT RULES

## 🎯 Project Doel
AI-powered CRM voor vloerwerk bedrijven met mobile PWA voor veldwerk en desktop CRM voor administratie.

## 🛠️ Tech Stack Requirements
- **Frontend**: Next.js 14 + TypeScript + Tailwind CSS
- **Database**: Prisma + SQLite (dev) / PostgreSQL (prod)
- **AI**: TensorFlow.js + Cloud Vision API
- **Mobile**: PWA met camera access
- **Forms**: React Hook Form + Zod validatie
- **State**: SWR voor data fetching
- **UI**: Radix UI components

## �� File Organization
```
src/
├── app/                 # Next.js app directory
│   ├── mobile/         # Mobile PWA routes
│   ├── desktop/        # Desktop CRM routes
│   └── api/           # API endpoints
├── components/         # React components
│   ├── ui/            # Reusable UI components
│   ├── shared/        # Shared business components
│   └── forms/         # Form components
├── lib/               # Services & utilities
├── types/             # TypeScript types
└── hooks/             # Custom hooks
```

## 🔧 Development Standards

### Code Quality
- ✅ TypeScript strict mode
- ✅ ESLint + Prettier configuratie
- ✅ Zod validatie voor alle forms
- ✅ Error boundaries voor major components
- ✅ Loading states voor async operations
- ✅ Nederlandse comments en variabelen

### Mobile-First Development
- ✅ Start altijd met mobile design
- ✅ Touch-friendly interface (44px minimum)
- ✅ Responsive design met Tailwind
- ✅ PWA functionaliteit
- ✅ Camera integration voor foto's

### Business Logic
- ✅ Nederlandse terminologie
- ✅ BTW berekeningen (21%)
- ✅ Project status: NIEUW → AKTIEF → VOLTOOID → GEFACTUREERD
- ✅ Automatische oppervlakte berekening
- ✅ AI prijsberekening met confidence scores

### Security & Performance
- ✅ Input validation met Zod
- ✅ File upload restrictions (10MB, images only)
- ✅ Rate limiting op API endpoints
- ✅ Image compression voor uploads
- ✅ Service worker voor caching
- ✅ Database query optimization

## 🚀 Deployment Checklist
- ✅ Environment variables configured
- ✅ Database migrations applied
- ✅ Static assets optimized
- ✅ Error monitoring setup
- ✅ Performance monitoring enabled
```

### 2. **USER RULES** - `.cursor/rules/user-rules.md`

```markdown
# 👤 VLOERWERK CRM - USER RULES

##  Jouw Workflow

### Development Workflow
- ✅ **Altijd TypeScript** gebruiken met strikte typing
- ✅ **Test elke component** in zowel mobile als desktop view
- ✅ **Commit vaak** met duidelijke Nederlandse commit messages
- ✅ **Gebruik feature branches** voor nieuwe functionaliteit
- ✅ **Review code** voor performance en accessibility

### Code Generation Preferences
- ✅ **Generate complete, working code** (geen placeholders)
- ✅ **Include comprehensive error handling**
- ✅ **Add clear comments** explaining complex logic
- ✅ **Use modern language features** en best practices
- ✅ **Implement proper logging** en debugging support
- ✅ **Include unit tests** when appropriate

### Project Setup Assistance
- ✅ **Provide complete dependency management** instructions
- ✅ **Include environment setup** steps (API keys, config files)
- ✅ **Add verification steps** to test if everything works
- ✅ **Include troubleshooting** voor common installation problems
- ✅ **Setup proper project structure** en organization

### AI Framework Expertise
- ✅ **Familiar with TensorFlow.js** voor lokale AI
- ✅ **Provide cloud AI alternatives** (Google Vision, Azure)
- ✅ **Suggest when to use which approach**
- ✅ **Include hands-on project examples**
- ✅ **Explain integration patterns** en architectures

### Mobile Development Focus
- ✅ **Prioritize user experience** en performance
- ✅ **Follow platform design guidelines** (iOS HIG, Material Design)
- ✅ **Implement proper state management**
- ✅ **Use reactive programming patterns**
- ✅ **Include accessibility features** by default
- ✅ **Optimize for different screen sizes** en orientations

### Code Quality Standards
- ✅ **Follow SOLID principles**
- ✅ **Use appropriate design patterns**
- ✅ **Implement proper separation of concerns**
- ✅ **Include comprehensive documentation**
- ✅ **Add meaningful variable and function names**
- ✅ **Ensure type safety** en null safety

### Security Best Practices
- ✅ **Never hardcode sensitive information**
- ✅ **Use secure storage** voor credentials
- ✅ **Implement proper authentication flows**
- ✅ **Validate all user inputs**
- ✅ **Use HTTPS** voor alle network communications
- ✅ **Follow OWASP guidelines** voor mobile apps

### Learning and Explanation Approach
- ✅ **Start with high-level concepts** before diving into code
- ✅ **Provide real-world analogies** voor complex topics
- ✅ **Include visual diagrams** of pseudocode when helpful
- ✅ **Offer multiple implementation approaches**
- ✅ **Explain performance implications** van verschillende keuzes
- ✅ **Suggest further learning resources**

## 🎯 Specifieke Requirements voor Vloerwerk CRM

### Business Domain Knowledge
- ✅ **Vloerwerk terminologie** (laminaat, parket, tegels, vinyl, natuursteen)
- ✅ **BTW berekeningen** (21% Nederlandse BTW)
- ✅ **Project workflow** (offerte → uitvoering → facturatie)
- ✅ **Materiaal prijzen** en arbeidskosten
- ✅ **Afmetingen berekeningen** (m², lengte × breedte)

### Technical Requirements
- ✅ **Device detection** (mobile vs desktop)
- ✅ **Camera integration** voor foto's
- ✅ **AI image analysis** voor materiaal detectie
- ✅ **Offline functionality** voor veldwerk
- ✅ **Real-time sync** wanneer online
- ✅ **PDF generation** voor offertes en facturen

### User Experience
- ✅ **Mobile-first design** voor veldwerkers
- ✅ **Touch-friendly interface** (44px minimum)
- ✅ **Intuitive navigation** voor niet-technische gebruikers
- ✅ **Clear error messages** in het Nederlands
- ✅ **Loading indicators** voor alle actions
- ✅ **Confirmation dialogs** voor destructive actions

### Performance Requirements
- ✅ **Fast image upload** en processing
- ✅ **Efficient database queries** voor grote datasets
- ✅ **Optimized AI analysis** (max 30 seconden)
- ✅ **Responsive UI** op alle devices
- ✅ **Offline capability** voor kritieke functies
```

### 3. **DEVELOPMENT GUIDELINES** - `DEVELOPMENT.md`

```markdown
# 🚀 VLOERWERK CRM - DEVELOPMENT GUIDELINES

##  Quick Start

### 1. Environment Setup
```bash
# Maak .env.local
DATABASE_URL="file:./dev.db"
NEXTAUTH_SECRET="your-secret-key"
AI_API_KEY="your-ai-api-key"
UPLOAD_DIR="./public/uploads"
```

### 2. Database Setup
```bash
npx prisma generate
npx prisma db push
```

### 3. Development
```bash
npm run dev
```

## ️ Project Structure

### Core Directories
- `src/app/` - Next.js app directory
- `src/components/` - React components
- `src/lib/` - Services & utilities
- `src/types/` - TypeScript types
- `src/hooks/` - Custom hooks

### Mobile PWA
- `src/app/mobile/` - Mobile routes
- `src/components/mobile/` - Mobile components
- Camera integration
- Offline functionality

### Desktop CRM
- `src/app/desktop/` - Desktop routes
- `src/components/desktop/` - Desktop components
- Dashboard & analytics
- Project management

## 🔧 Development Workflow

### 1. Feature Development
1. Maak feature branch: `git checkout -b feature/nieuwe-functie`
2. Implementeer volgens project rules
3. Test in mobile en desktop view
4. Commit met Nederlandse message
5. Push en maak pull request

### 2. Code Quality
- TypeScript strict mode
- ESLint + Prettier
- Zod validatie voor forms
- Error boundaries
- Loading states

### 3. Testing
- Unit tests voor utilities
- Integration tests voor API
- E2E tests voor critical flows
- Mobile responsiveness testing

## 🎯 Business Logic

### Project Status Flow
```
NIEUW → AKTIEF → VOLTOOID → GEFACTUREERD
```

### Prijsberekening
```typescript
// BTW (21%)
const prijsInclusiefBtw = (bedrag: number) => bedrag * 1.21;

// Oppervlakte
const berekenOppervlakte = (lengte: number, breedte: number) => 
  Math.round((lengte * breedte + Number.EPSILON) * 100) / 100;
```

### Materiaal Types
- Laminaat
- Parket
- Tegels
- Vinyl
- Natuursteen

## 📱 Mobile-First Development

### Device Detection
```typescript
const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;
```

### Touch-Friendly Interface
- Minimum 44px voor touch targets
- Responsive design met Tailwind
- PWA functionaliteit

### Camera Integration
- Native camera access
- Image compression
- Offline storage

## 🤖 AI Integration

### Vision Analysis Pipeline
1. Foto upload & preprocessing
2. AI Vision API call
3. Materiaal detectie
4. Conditie assessment
5. Prijsberekening
6. Database storage

### Fallback Strategy
- Cloud AI (Google Vision) voor accuracy
- Lokale AI (TensorFlow.js) voor privacy
- Confidence thresholds
- Retry logic

## 🔒 Security & Performance

### Security
- Input validation met Zod
- File upload restrictions
- Rate limiting
- HTTPS in productie

### Performance
- Image compression
- Lazy loading
- Service worker caching
- Database optimization

## 🚀 Deployment

### Vercel (Aanbevolen)
```bash
npx vercel --prod
```

### Environment Variables
- `DATABASE_URL`
- `AI_API_KEY`
- `NEXTAUTH_SECRET`

### Database Migration
```bash
npx prisma migrate deploy
```

## 🆘 Troubleshooting

### Common Issues
- Prisma schema sync: `npx prisma db push`
- Image upload fails: Check permissions
- AI analysis timeouts: Increase API timeout
- Mobile redirect issues: Check device detection

## 📞 Support

Voor vragen over development:
1. Check project rules
2. Review user rules
3. Consult setup instructions
4. Check troubleshooting guide
```

### 4. **CONTRIBUTING GUIDELINES** - `CONTRIBUTING.md`

```markdown
#  CONTRIBUTING - VLOERWERK CRM

## 🎯 Bijdragen aan het Project

### Development Workflow

#### 1. Setup
```bash
# Fork en clone het project
git clone https://github.com/your-username/vloerwerk-crm.git
cd vloerwerk-crm

# Installeer dependencies
npm install

# Setup environment
cp .env.example .env.local
# Vul de environment variables in

# Setup database
npx prisma generate
npx prisma db push

# Start development
npm run dev
```

#### 2. Feature Development
```bash
# Maak feature branch
git checkout -b feature/nieuwe-functie

# Implementeer je feature
# Volg de project rules en user rules

# Test je code
npm run test
npm run type-check
npm run lint

# Commit met duidelijke Nederlandse message
git commit -m "feat: voeg nieuwe project status toe"

# Push en maak pull request
git push origin feature/nieuwe-functie
```

## 📋 Code Standards

### TypeScript
- Gebruik strict mode
- Definieer interfaces voor alle props
- Geen `any` types
- Proper error handling

### React Components
```typescript
// ✅ Goed
interface ProjectCardProps {
  project: Project;
  onEdit?: (id: string) => void;
}

export const ProjectCard: React.FC<ProjectCardProps> = ({ project, onEdit }) => {
  // Component logic
};

// ❌ Slecht
export const ProjectCard = ({ project, onEdit }: any) => {
  // Component logic
};
```

### Forms & Validation
```typescript
// Gebruik Zod voor validatie
const projectSchema = z.object({
  naam: z.string().min(1, "Naam is verplicht"),
  klantId: z.string().uuid("Ongeldige klant ID"),
  lengte: z.number().positive("Lengte moet positief zijn"),
  breedte: z.number().positive("Breedte moet positief zijn")
});
```

### Styling
```typescript
// Gebruik Tailwind classes
// ✅ Goed
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">

// ❌ Slecht
<div style={{ display: 'flex', alignItems: 'center', padding: '16px' }}>
```

## 🎯 Business Logic

### Nederlandse Terminologie
- Gebruik Nederlandse variabelen en functienamen
- Alle UI teksten in het Nederlands
- Comments in het Nederlands

```typescript
// ✅ Goed
const berekenOppervlakte = (lengte: number, breedte: number) => {
  return lengte * breedte;
};

// ❌ Slecht
const calculateArea = (length: number, width: number) => {
  return length * width;
};
```

### BTW Berekeningen
```typescript
// Altijd 21% BTW
const prijsInclusiefBtw = (bedrag: number) => bedrag * 1.21;
const btwBedrag = (bedrag: number) => bedrag * 0.21;
```

### Project Status
```typescrip


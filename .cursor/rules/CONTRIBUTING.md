# �� CONTRIBUTING - VLOERWERK CRM

## 🎯 Bij<PERSON>gen aan het Project

### Development Workflow

#### 1. Setup
```bash
# Fork en clone het project
git clone https://github.com/your-username/vloerwerk-crm.git
cd vloerwerk-crm

# Installeer dependencies
npm install

# Setup environment
cp .env.example .env.local
# Vul de environment variables in

# Setup database
npx prisma generate
npx prisma db push

# Start development
npm run dev
```

#### 2. Feature Development
```bash
# Maak feature branch
git checkout -b feature/nieuwe-functie

# Implementeer je feature
# Volg de project rules en user rules

# Test je code
npm run test
npm run type-check
npm run lint

# Commit met duidelijke Nederlandse message
git commit -m "feat: voeg nieuwe project status toe"

# Push en maak pull request
git push origin feature/nieuwe-functie
```

## 📋 Code Standards

### TypeScript
- Gebruik strict mode
- Definieer interfaces voor alle props
- Geen `any` types
- Proper error handling

### React Components
```typescript
// ✅ Goed
interface ProjectCardProps {
  project: Project;
  onEdit?: (id: string) => void;
}

export const ProjectCard: React.FC<ProjectCardProps> = ({ project, onEdit }) => {
  // Component logic
};

// ❌ Slecht
export const ProjectCard = ({ project, onEdit }: any) => {
  // Component logic
};
```

### Forms & Validation
```typescript
// Gebruik Zod voor validatie
const projectSchema = z.object({
  naam: z.string().min(1, "Naam is verplicht"),
  klantId: z.string().uuid("Ongeldige klant ID"),
  lengte: z.number().positive("Lengte moet positief zijn"),
  breedte: z.number().positive("Breedte moet positief zijn")
});
```

### Styling
```typescript
// Gebruik Tailwind classes
// ✅ Goed
<div className="flex items-center justify-between p-4 bg-white rounded-lg shadow-sm">

// ❌ Slecht
<div style={{ display: 'flex', alignItems: 'center', padding: '16px' }}>
```

## 🎯 Business Logic

### Nederlandse Terminologie
- Gebruik Nederlandse variabelen en functienamen
- Alle UI teksten in het Nederlands
- Comments in het Nederlands

```typescript
// ✅ Goed
const berekenOppervlakte = (lengte: number, breedte: number) => {
  return lengte * breedte;
};

// ❌ Slecht
const calculateArea = (length: number, width: number) => {
  return length * width;
};
```

### BTW Berekeningen
```typescript
// Altijd 21% BTW
const prijsInclusiefBtw = (bedrag: number) => bedrag * 1.21;
const btwBedrag = (bedrag: number) => bedrag * 0.21;
```

### Project Status
```typescript
enum ProjectStatus {
  NIEUW = 'NIEUW',
  AKTIEF = 'AKTIEF',
  VOLTOOID = 'VOLTOOID',
  GEFACTUREERD = 'GEFACTUREERD'
}
```

## 📱 Mobile-First Development

### Responsive Design
```typescript
// Device detection
const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

// Automatische redirect
useEffect(() => {
  if (isMobile && window.location.pathname === '/') {
    window.location.href = '/mobile';
  } else if (!isMobile && window.location.pathname === '/') {
    window.location.href = '/desktop';
  }
}, []);
```

### Touch-Friendly Interface
- Minimum 44px voor touch targets
- Proper spacing tussen elementen
- Clear visual feedback

## 🤖 AI Integration

### AI Service Pattern
```typescript
interface AIAnalysisResult {
  materiaalType: string;
  conditieScore: number;
  geschatteKosten: number;
  confidence: number;
}

class AIService {
  async analyzeFloorImages(files: File[], afmetingen: Afmetingen): Promise<AIAnalysisResult> {
    // AI logic
  }
}
```

### Error Handling
```typescript
try {
  const result = await aiService.analyzeFloorImages(files, afmetingen);
  return result;
} catch (error) {
  console.error('AI analysis failed:', error);
  // Fallback logic
  return getDefaultAnalysis();
}
```

## 🧪 Testing

### Unit Tests
```typescript
// src/lib/__tests__/utils.test.ts
import { berekenOppervlakte, prijsInclusiefBtw } from '../utils';

describe('Utils', () => {
  test('berekenOppervlakte', () => {
    expect(berekenOppervlakte(5, 3)).toBe(15);
  });

  test('prijsInclusiefBtw', () => {
    expect(prijsInclusiefBtw(100)).toBe(121);
  });
});
```

### Integration Tests
```typescript
// src/app/api/__tests__/projecten.test.ts
import { POST } from '../projecten/route';

describe('Projecten API', () => {
  test('POST /api/projecten', async () => {
    const request = new Request('http://localhost:3000/api/projecten', {
      method: 'POST',
      body: JSON.stringify({
        naam: 'Test Project',
        klantId: 'test-id'
      })
    });

    const response = await POST(request);
    expect(response.status).toBe(200);
  });
});
```

## 📝 Commit Messages

### Format
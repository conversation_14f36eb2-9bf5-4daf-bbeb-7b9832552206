# Vloerwerk CRM - Cursor AI Rules

## Project Context
Dit is een Next.js/TypeScript CRM applicatie voor vloerwerk bedrijven met AI-analyse functionaliteit.

## USER WORKFLOW RULES

### Development Workflow
- Altijd TypeScript gebruiken met strikte typing
- Test elke component in zowel mobile als desktop view
- Commit vaak met duidelijke Nederlandse commit messages
- Gebruik feature branches voor nieuwe functionaliteit
- Review code voor performance en accessibility

### File Organization
- Nieuwe components altijd in `/src/components/`
- Business logic in `/src/lib/`
- Types centraal in `/src/types/`
- API routes volgen RESTful conventions
- Utilities in `/src/lib/utils.ts`

### Code Quality Standards
- Gebruik ESLint en Prettier configuratie
- Minimaal 80% test coverage voor business logic
- Alle forms met Zod validatie
- Error boundaries voor alle major components
- Loading states voor alle async operations

### Mobile-First Development
- Start altijd met mobile design
- Test op verschillende schermgroottes
- Touch-friendly interface (44px minimum)
- Offline functionality waar mogelijk
- Camera integration voor foto's

### AI Integration Guidelines
- Cache alle AI resultaten in database
- Implement retry logic met exponential backoff
- Rate limiting voor API calls
- Confidence thresholds voor AI decisions
- Fallback voor AI failures

### Security Checklist
- Valideer alle user inputs
- Sanitize database queries
- File upload restrictions (10MB, image types only)
- Rate limiting op API endpoints
- HTTPS in productie

### Performance Requirements
- Lazy loading voor images
- Code splitting per route
- Database query optimization
- Image compression voor uploads
- Service worker voor caching

### Business Logic Rules
- Nederlandse terminologie voor vloerwerk
- BTW berekeningen (21%)
- Project status flow: NIEUW → AKTIEF → VOLTOOID → GEFACTUREERD
- Automatische oppervlakte berekening
- Material pricing met complexity factors

### User Experience Standards
- Responsive design (mobile-first)
- Intuitive navigation
- Clear error messages in Nederlands
- Loading indicators voor alle actions
- Confirmation dialogs voor destructive actions

### Testing Requirements
- Unit tests voor utilities en services
- Integration tests voor API routes
- E2E tests voor critical user flows
- Mobile responsiveness testing
- Accessibility testing

### Deployment Checklist
- Environment variables configured
- Database migrations applied
- Static assets optimized
- Error monitoring setup
- Performance monitoring enabled


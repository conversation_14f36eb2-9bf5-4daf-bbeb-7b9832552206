<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BouwPro CRM - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        .nav-item {
            transition: all 0.2s ease;
        }
        .nav-item:hover {
            background-color: #f3f4f6;
        }
        .nav-item.active {
            background-color: #3b82f6;
            color: white;
        }
        .calculator-field {
            transition: all 0.3s ease;
        }
        .fade-in {
            animation: fadeIn 0.3s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="text-2xl">🏗️</div>
                    <h1 class="text-xl font-bold text-gray-900">BouwPro CRM</h1>
                </div>
                
                <!-- Header Actions -->
                <div class="flex items-center space-x-4">
                    <button class="text-gray-500 hover:text-gray-700">
                        <span class="text-xl">🔔</span>
                    </button>
                    <button class="text-gray-500 hover:text-gray-700">
                        <span class="text-xl">👤</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="bg-white border-b">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex space-x-8 overflow-x-auto">
                <button onclick="showSection('dashboard')" class="nav-item px-3 py-4 text-sm font-medium rounded-t-lg active">
                    Dashboard
                </button>
                <button onclick="showSection('projecten')" class="nav-item px-3 py-4 text-sm font-medium rounded-t-lg">
                    Projecten
                </button>
                <button onclick="showSection('calculators')" class="nav-item px-3 py-4 text-sm font-medium rounded-t-lg">
                    Calculators
                </button>
                <button onclick="showSection('klanten')" class="nav-item px-3 py-4 text-sm font-medium rounded-t-lg">
                    Klanten
                </button>
                <button onclick="showSection('materialen')" class="nav-item px-3 py-4 text-sm font-medium rounded-t-lg">
                    Materialen
                </button>
                <button onclick="showSection('ai-assistent')" class="nav-item px-3 py-4 text-sm font-medium rounded-t-lg">
                    AI Assistent
                </button>
                <button onclick="showSection('instellingen')" class="nav-item px-3 py-4 text-sm font-medium rounded-t-lg">
                    Instellingen
                </button>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Dashboard Section -->
        <div id="dashboard-section" class="section">
            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <div class="text-3xl mr-4">📊</div>
                        <div>
                            <p class="text-sm text-gray-600">Actieve Projecten</p>
                            <p class="text-2xl font-bold text-gray-900">6</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <div class="text-3xl mr-4">📋</div>
                        <div>
                            <p class="text-sm text-gray-600">Nieuwe Offertes</p>
                            <p class="text-2xl font-bold text-gray-900">2</p>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="flex items-center">
                        <div class="text-3xl mr-4">💰</div>
                        <div>
                            <p class="text-sm text-gray-600">Gem. Projectwaarde</p>
                            <p class="text-2xl font-bold text-gray-900">€2.500</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Bouwtype Selector -->
            <div class="bg-white rounded-lg p-6 shadow-sm mb-8">
                <h2 class="text-lg font-semibold mb-4">🏗️ Kies Bouwtype</h2>
                <select id="bouwtype-selector" onchange="changeBouwtype()" class="w-full p-3 border border-gray-300 rounded-lg text-lg">
                    <option value="vloer">🏠 Vloerwerk</option>
                    <option value="muur">🧱 Muurwerk</option>
                    <option value="ramen">🪟 Ramen & Kozijnen</option>
                    <option value="dak">🏠 Dakwerk</option>
                    <option value="badkamer">🚿 Badkamer</option>
                    <option value="keuken">🍳 Keuken</option>
                    <option value="tuin">🌱 Tuinwerk</option>
                </select>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <button onclick="openCamera()" class="bg-blue-500 text-white p-6 rounded-lg text-center hover:bg-blue-600 transition-colors">
                    <div class="text-3xl mb-2">📸</div>
                    <div class="font-medium">Camera Capture</div>
                    <div class="text-sm opacity-80">Maak foto's voor analyse</div>
                </button>
                
                <button onclick="startAIAnalyse()" class="bg-green-500 text-white p-6 rounded-lg text-center hover:bg-green-600 transition-colors">
                    <div class="text-3xl mb-2">🤖</div>
                    <div class="font-medium">AI Analyse</div>
                    <div class="text-sm opacity-80">Automatische detectie</div>
                </button>
                
                <button onclick="toggleOfflineMode()" class="bg-purple-500 text-white p-6 rounded-lg text-center hover:bg-purple-600 transition-colors">
                    <div class="text-3xl mb-2">📱</div>
                    <div class="font-medium">Offline Werken</div>
                    <div class="text-sm opacity-80">Sync later</div>
                </button>
            </div>

            <!-- Dynamic Calculator -->
            <div class="bg-white rounded-lg p-6 shadow-sm mb-8">
                <h2 class="text-lg font-semibold mb-4">🧮 <span id="calculator-title">Vloerwerk Calculator</span></h2>
                <div id="calculator-fields" class="space-y-4">
                    <!-- Dynamic fields will be inserted here -->
                </div>
                
                <div class="flex space-x-4 mt-6">
                    <button onclick="calculatePrice()" class="flex-1 bg-blue-500 text-white py-3 rounded-lg font-medium hover:bg-blue-600 transition-colors">
                        💰 Bereken Prijs
                    </button>
                    <button onclick="generateOfferte()" class="flex-1 bg-green-500 text-white py-3 rounded-lg font-medium hover:bg-green-600 transition-colors">
                        📋 Genereer Offerte
                    </button>
                </div>
                
                <div id="calculation-result" class="mt-4 p-4 bg-gray-50 rounded-lg hidden">
                    <!-- Results will be shown here -->
                </div>
            </div>

            <!-- Recent Activities -->
            <div class="bg-white rounded-lg p-6 shadow-sm mb-8">
                <h2 class="text-lg font-semibold mb-4">📈 Laatste Activiteiten</h2>
                <div class="space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">🏠</span>
                            <div>
                                <p class="font-medium">Woonkamer Renovatie - Familie van der Berg</p>
                                <p class="text-sm text-gray-600">Offerte gegenereerd • €1.250</p>
                            </div>
                        </div>
                        <span class="text-sm text-gray-500">2 uur geleden</span>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">🧱</span>
                            <div>
                                <p class="font-medium">Tuinmuur Project - Bedrijf ABC</p>
                                <p class="text-sm text-gray-600">AI analyse voltooid • €3.200</p>
                            </div>
                        </div>
                        <span class="text-sm text-gray-500">1 dag geleden</span>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">🪟</span>
                            <div>
                                <p class="font-medium">Raamvervanging - Mevrouw Jansen</p>
                                <p class="text-sm text-gray-600">Project gestart • €850</p>
                            </div>
                        </div>
                        <span class="text-sm text-gray-500">3 dagen geleden</span>
                    </div>
                </div>
            </div>

            <!-- AI Tip -->
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-6">
                <div class="flex items-center space-x-3">
                    <span class="text-2xl">🤖</span>
                    <div>
                        <h3 class="font-semibold">AI Tip van de dag</h3>
                        <p class="opacity-90">Voorzie extra tijd bij buitenwerken in de regen. Check het weerbericht voor optimale planning!</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Other sections (hidden by default) -->
        <div id="calculators-section" class="section hidden">
            <h2 class="text-2xl font-bold mb-6">🧮 Calculators</h2>
            <p class="text-gray-600">Calculator sectie komt hier...</p>
        </div>

        <div id="projecten-section" class="section hidden">
            <h2 class="text-2xl font-bold mb-6">📋 Projecten</h2>
            <p class="text-gray-600">Projecten overzicht komt hier...</p>
        </div>

        <div id="klanten-section" class="section hidden">
            <h2 class="text-2xl font-bold mb-6">👥 Klanten</h2>
            <p class="text-gray-600">Klanten database komt hier...</p>
        </div>

        <div id="materialen-section" class="section hidden">
            <h2 class="text-2xl font-bold mb-6">📦 Materialen</h2>
            <p class="text-gray-600">Materialen beheer komt hier...</p>
        </div>

        <div id="ai-assistent-section" class="section hidden">
            <h2 class="text-2xl font-bold mb-6">🤖 AI Assistent</h2>
            <p class="text-gray-600">AI Assistent interface komt hier...</p>
        </div>

        <div id="instellingen-section" class="section hidden">
            <h2 class="text-2xl font-bold mb-6">⚙️ Instellingen</h2>
            <p class="text-gray-600">Instellingen paneel komt hier...</p>
        </div>
    </main>

    <script>
        // Calculator configurations for different building types
        const calculatorConfigs = {
            vloer: {
                title: "Vloerwerk Calculator",
                fields: [
                    { name: "lengte", label: "Lengte (m)", type: "number", placeholder: "5.0" },
                    { name: "breedte", label: "Breedte (m)", type: "number", placeholder: "4.0" },
                    { name: "materiaal", label: "Materiaal Type", type: "select", options: [
                        { value: "laminaat", label: "Laminaat (€15/m²)" },
                        { value: "parket", label: "Parket (€35/m²)" },
                        { value: "vinyl", label: "Vinyl (€20/m²)" },
                        { value: "tegels", label: "Tegels (€25/m²)" }
                    ]},
                    { name: "ondervloer", label: "Ondervloer nodig?", type: "checkbox" }
                ],
                calculate: (data) => {
                    const area = data.lengte * data.breedte;
                    const prices = { laminaat: 15, parket: 35, vinyl: 20, tegels: 25 };
                    const materialCost = area * prices[data.materiaal];
                    const onderVloerCost = data.ondervloer ? area * 5 : 0;
                    const laborCost = area * 12;
                    return {
                        area: area,
                        materialCost: materialCost,
                        onderVloerCost: onderVloerCost,
                        laborCost: laborCost,
                        total: materialCost + onderVloerCost + laborCost
                    };
                }
            },
            muur: {
                title: "Muurwerk Calculator",
                fields: [
                    { name: "lengte", label: "Lengte (m)", type: "number", placeholder: "10.0" },
                    { name: "hoogte", label: "Hoogte (m)", type: "number", placeholder: "2.5" },
                    { name: "materiaal", label: "Materiaal Type", type: "select", options: [
                        { value: "baksteen", label: "Baksteen (€45/m²)" },
                        { value: "kalkzandsteen", label: "Kalkzandsteen (€35/m²)" },
                        { value: "beton", label: "Betonblokken (€25/m²)" }
                    ]},
                    { name: "isolatie", label: "Isolatie toevoegen?", type: "checkbox" }
                ],
                calculate: (data) => {
                    const area = data.lengte * data.hoogte;
                    const prices = { baksteen: 45, kalkzandsteen: 35, beton: 25 };
                    const materialCost = area * prices[data.materiaal];
                    const isolatieCost = data.isolatie ? area * 15 : 0;
                    const laborCost = area * 25;
                    return {
                        area: area,
                        materialCost: materialCost,
                        isolatieCost: isolatieCost,
                        laborCost: laborCost,
                        total: materialCost + isolatieCost + laborCost
                    };
                }
            },
            ramen: {
                title: "Ramen & Kozijnen Calculator",
                fields: [
                    { name: "aantal", label: "Aantal ramen", type: "number", placeholder: "4" },
                    { name: "breedte", label: "Gem. breedte (m)", type: "number", placeholder: "1.2" },
                    { name: "hoogte", label: "Gem. hoogte (m)", type: "number", placeholder: "1.5" },
                    { name: "type", label: "Raam Type", type: "select", options: [
                        { value: "kunststof", label: "Kunststof (€350/m²)" },
                        { value: "hout", label: "Hout (€450/m²)" },
                        { value: "aluminium", label: "Aluminium (€550/m²)" }
                    ]},
                    { name: "driedubbel", label: "Driedubbel glas?", type: "checkbox" }
                ],
                calculate: (data) => {
                    const areaPerRaam = data.breedte * data.hoogte;
                    const totalArea = areaPerRaam * data.aantal;
                    const prices = { kunststof: 350, hout: 450, aluminium: 550 };
                    const materialCost = totalArea * prices[data.type];
                    const glasUpgrade = data.driedubbel ? totalArea * 50 : 0;
                    const laborCost = data.aantal * 150;
                    return {
                        aantal: data.aantal,
                        totalArea: totalArea,
                        materialCost: materialCost,
                        glasUpgrade: glasUpgrade,
                        laborCost: laborCost,
                        total: materialCost + glasUpgrade + laborCost
                    };
                }
            }
        };

        let currentBouwtype = 'vloer';

        // Initialize calculator
        function initCalculator() {
            changeBouwtype();
        }

        // Change building type and update calculator
        function changeBouwtype() {
            const selector = document.getElementById('bouwtype-selector');
            currentBouwtype = selector.value;
            const config = calculatorConfigs[currentBouwtype];
            
            if (!config) return;
            
            // Update title
            document.getElementById('calculator-title').textContent = config.title;
            
            // Update fields
            const fieldsContainer = document.getElementById('calculator-fields');
            fieldsContainer.innerHTML = '';
            fieldsContainer.className = 'space-y-4 fade-in';
            
            config.fields.forEach(field => {
                const fieldDiv = document.createElement('div');
                fieldDiv.className = 'calculator-field';
                
                if (field.type === 'select') {
                    fieldDiv.innerHTML = `
                        <label class="block text-sm font-medium text-gray-700 mb-2">${field.label}</label>
                        <select name="${field.name}" class="w-full px-3 py-2 border border-gray-300 rounded-md">
                            ${field.options.map(opt => `<option value="${opt.value}">${opt.label}</option>`).join('')}
                        </select>
                    `;
                } else if (field.type === 'checkbox') {
                    fieldDiv.innerHTML = `
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" name="${field.name}" class="rounded">
                            <span class="text-sm font-medium text-gray-700">${field.label}</span>
                        </label>
                    `;
                } else {
                    fieldDiv.innerHTML = `
                        <label class="block text-sm font-medium text-gray-700 mb-2">${field.label}</label>
                        <input type="${field.type}" name="${field.name}" placeholder="${field.placeholder || ''}" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-md">
                    `;
                }
                
                fieldsContainer.appendChild(fieldDiv);
            });
        }

        // Calculate price based on current building type
        function calculatePrice() {
            const config = calculatorConfigs[currentBouwtype];
            if (!config) return;
            
            const formData = {};
            const fields = document.querySelectorAll('#calculator-fields input, #calculator-fields select');
            
            fields.forEach(field => {
                if (field.type === 'checkbox') {
                    formData[field.name] = field.checked;
                } else if (field.type === 'number') {
                    formData[field.name] = parseFloat(field.value) || 0;
                } else {
                    formData[field.name] = field.value;
                }
            });
            
            const result = config.calculate(formData);
            displayResult(result);
        }

        // Display calculation result
        function displayResult(result) {
            const resultDiv = document.getElementById('calculation-result');
            let html = '<h3 class="font-semibold text-lg mb-3">💰 Berekening Resultaat</h3>';
            
            Object.entries(result).forEach(([key, value]) => {
                if (typeof value === 'number') {
                    if (key.includes('Cost') || key === 'total') {
                        html += `<div class="flex justify-between py-1">
                            <span class="capitalize">${key.replace('Cost', ' Kosten')}:</span>
                            <span class="font-medium">€${value.toFixed(2)}</span>
                        </div>`;
                    } else {
                        html += `<div class="flex justify-between py-1">
                            <span class="capitalize">${key}:</span>
                            <span class="font-medium">${value.toFixed(2)}${key.includes('area') || key.includes('Area') ? ' m²' : ''}</span>
                        </div>`;
                    }
                }
            });
            
            if (result.total) {
                html += `<div class="border-t pt-2 mt-2">
                    <div class="flex justify-between text-lg font-bold">
                        <span>Totaal:</span>
                        <span class="text-blue-600">€${result.total.toFixed(2)}</span>
                    </div>
                </div>`;
            }
            
            resultDiv.innerHTML = html;
            resultDiv.classList.remove('hidden');
        }

        // Generate offerte
        function generateOfferte() {
            alert('📋 Offerte wordt gegenereerd!\n\nDit zou een PDF genereren met:\n• Projectdetails\n• Materiaallijst\n• Prijsberekening\n• Voorwaarden');
        }

        // Navigation functions
        function showSection(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.add('hidden');
            });
            
            // Show selected section
            document.getElementById(sectionName + '-section').classList.remove('hidden');
            
            // Update navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        // Quick action functions
        function openCamera() {
            window.location.href = 'camera.html';
        }

        function startAIAnalyse() {
            alert('🤖 AI Analyse gestart!\n\nDit zou:\n• Foto\'s analyseren\n• Materialen detecteren\n• Schade beoordelen\n• Prijzen berekenen');
        }

        function toggleOfflineMode() {
            alert('📱 Offline modus!\n\nJe kunt nu:\n• Projecten maken zonder internet\n• Foto\'s lokaal opslaan\n• Later synchroniseren');
        }

        // Initialize on page load
        window.addEventListener('load', initCalculator);
    </script>
</body>
</html>

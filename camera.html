<!DOCTYPE html>
<html lang="nl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera - Vloerwerk CRM</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        #video {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .camera-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }
        .camera-grid {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(to right, rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(to bottom, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 33.33% 33.33%;
        }
        .capture-button {
            width: 70px;
            height: 70px;
            border: 4px solid white;
            border-radius: 50%;
            background: rgba(255,255,255,0.3);
            transition: all 0.2s ease;
        }
        .capture-button:active {
            transform: scale(0.95);
            background: rgba(255,255,255,0.5);
        }
    </style>
</head>
<body class="min-h-screen bg-black">
    <!-- Header -->
    <div class="absolute top-0 left-0 right-0 z-10 bg-gradient-to-b from-black/50 to-transparent">
        <div class="flex items-center justify-between p-4 text-white">
            <button onclick="goBack()" class="flex items-center space-x-2">
                <span class="text-xl">←</span>
                <span>Terug</span>
            </button>
            <h1 class="text-lg font-medium">Camera</h1>
            <button onclick="toggleFlash()" id="flash-btn" class="text-xl">⚡</button>
        </div>
    </div>

    <!-- Camera View -->
    <div class="relative w-full h-screen">
        <video id="video" autoplay playsinline></video>
        
        <!-- Camera Overlay -->
        <div class="camera-overlay">
            <!-- Grid Lines -->
            <div class="camera-grid opacity-30"></div>
            
            <!-- Focus Area -->
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-32 h-32 border-2 border-white/50 rounded-lg"></div>
        </div>

        <!-- Camera Controls -->
        <div class="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent">
            <div class="flex items-center justify-between p-6">
                <!-- Gallery Button -->
                <button onclick="openGallery()" class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center text-white">
                    <span class="text-xl">🖼️</span>
                </button>

                <!-- Capture Button -->
                <button onclick="capturePhoto()" class="capture-button flex items-center justify-center">
                    <div class="w-12 h-12 bg-white rounded-full"></div>
                </button>

                <!-- Switch Camera -->
                <button onclick="switchCamera()" class="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center text-white">
                    <span class="text-xl">🔄</span>
                </button>
            </div>

            <!-- Photo Count -->
            <div class="text-center pb-4">
                <span class="text-white/80 text-sm">Foto's: <span id="photo-count">0</span></span>
            </div>
        </div>
    </div>

    <!-- Photo Preview Modal -->
    <div id="photo-modal" class="fixed inset-0 bg-black z-50 hidden">
        <div class="flex flex-col h-full">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-4 text-white bg-black/50">
                <button onclick="closePreview()" class="text-xl">✕</button>
                <h2 class="text-lg font-medium">Foto Preview</h2>
                <button onclick="savePhoto()" class="text-blue-400">Opslaan</button>
            </div>

            <!-- Photo Preview -->
            <div class="flex-1 flex items-center justify-center">
                <img id="preview-image" class="max-w-full max-h-full object-contain">
            </div>

            <!-- Photo Actions -->
            <div class="p-4 bg-black/50">
                <div class="mb-4">
                    <label class="block text-white text-sm mb-2">Beschrijving:</label>
                    <input 
                        type="text" 
                        id="photo-description" 
                        placeholder="Bijv. Woonkamer vloer, schade aan hoek..."
                        class="w-full px-3 py-2 bg-white/10 text-white placeholder-white/50 rounded-lg border border-white/20"
                    >
                </div>
                <div class="flex space-x-3">
                    <button onclick="retakePhoto()" class="flex-1 bg-gray-600 text-white py-3 rounded-lg">
                        Opnieuw
                    </button>
                    <button onclick="savePhoto()" class="flex-1 bg-blue-500 text-white py-3 rounded-lg">
                        Opslaan
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Gallery Modal -->
    <div id="gallery-modal" class="fixed inset-0 bg-black z-50 hidden">
        <div class="flex flex-col h-full">
            <div class="flex items-center justify-between p-4 text-white bg-black/50">
                <button onclick="closeGallery()" class="text-xl">✕</button>
                <h2 class="text-lg font-medium">Foto Galerij</h2>
                <button onclick="clearGallery()" class="text-red-400">Wis alles</button>
            </div>
            
            <div id="gallery-grid" class="flex-1 p-4 grid grid-cols-3 gap-2 overflow-y-auto">
                <!-- Photos will be added here -->
            </div>
        </div>
    </div>

    <script>
        let video;
        let canvas;
        let photos = [];
        let currentStream;
        let facingMode = 'environment'; // 'user' for front camera
        let flashEnabled = false;

        // Initialize camera
        async function initCamera() {
            video = document.getElementById('video');
            canvas = document.createElement('canvas');
            
            try {
                await startCamera();
            } catch (error) {
                console.error('Camera error:', error);
                showError('Camera toegang geweigerd. Controleer je browser instellingen.');
            }
        }

        async function startCamera() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
            }

            const constraints = {
                video: {
                    facingMode: facingMode,
                    width: { ideal: 1920 },
                    height: { ideal: 1080 }
                }
            };

            try {
                currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                video.srcObject = currentStream;
            } catch (error) {
                // Fallback to basic constraints
                const basicConstraints = { video: true };
                currentStream = await navigator.mediaDevices.getUserMedia(basicConstraints);
                video.srcObject = currentStream;
            }
        }

        function capturePhoto() {
            const context = canvas.getContext('2d');
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            
            context.drawImage(video, 0, 0);
            
            const imageData = canvas.toDataURL('image/jpeg', 0.8);
            showPhotoPreview(imageData);
        }

        function showPhotoPreview(imageData) {
            document.getElementById('preview-image').src = imageData;
            document.getElementById('photo-modal').classList.remove('hidden');
            document.getElementById('photo-description').value = '';
        }

        function closePreview() {
            document.getElementById('photo-modal').classList.add('hidden');
        }

        function retakePhoto() {
            closePreview();
        }

        function savePhoto() {
            const imageData = document.getElementById('preview-image').src;
            const description = document.getElementById('photo-description').value || 'Vloer foto';
            
            const photo = {
                id: Date.now(),
                data: imageData,
                description: description,
                timestamp: new Date().toLocaleString('nl-NL')
            };
            
            photos.push(photo);
            updatePhotoCount();
            closePreview();
            
            showNotification(`✅ Foto opgeslagen: ${description}`);
        }

        function updatePhotoCount() {
            document.getElementById('photo-count').textContent = photos.length;
        }

        function switchCamera() {
            facingMode = facingMode === 'environment' ? 'user' : 'environment';
            startCamera();
        }

        function toggleFlash() {
            flashEnabled = !flashEnabled;
            const flashBtn = document.getElementById('flash-btn');
            flashBtn.textContent = flashEnabled ? '⚡' : '🔦';
            
            if (currentStream) {
                const track = currentStream.getVideoTracks()[0];
                if (track.getCapabilities && track.getCapabilities().torch) {
                    track.applyConstraints({
                        advanced: [{ torch: flashEnabled }]
                    });
                }
            }
        }

        function openGallery() {
            updateGallery();
            document.getElementById('gallery-modal').classList.remove('hidden');
        }

        function closeGallery() {
            document.getElementById('gallery-modal').classList.add('hidden');
        }

        function updateGallery() {
            const grid = document.getElementById('gallery-grid');
            grid.innerHTML = '';
            
            if (photos.length === 0) {
                grid.innerHTML = '<div class="col-span-3 text-center text-white/50 py-8">Nog geen foto\'s gemaakt</div>';
                return;
            }
            
            photos.forEach((photo, index) => {
                const photoElement = document.createElement('div');
                photoElement.className = 'relative aspect-square bg-gray-800 rounded-lg overflow-hidden';
                photoElement.innerHTML = `
                    <img src="${photo.data}" class="w-full h-full object-cover">
                    <div class="absolute bottom-0 left-0 right-0 bg-black/50 text-white text-xs p-1 truncate">
                        ${photo.description}
                    </div>
                    <button onclick="deletePhoto(${index})" class="absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full text-xs">✕</button>
                `;
                grid.appendChild(photoElement);
            });
        }

        function deletePhoto(index) {
            photos.splice(index, 1);
            updatePhotoCount();
            updateGallery();
        }

        function clearGallery() {
            if (confirm('Alle foto\'s verwijderen?')) {
                photos = [];
                updatePhotoCount();
                updateGallery();
            }
        }

        function goBack() {
            window.location.href = 'mobile.html';
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'fixed top-16 left-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg text-center z-50';
            notification.textContent = message;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        function showError(message) {
            const error = document.createElement('div');
            error.className = 'fixed top-16 left-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg text-center z-50';
            error.textContent = message;
            document.body.appendChild(error);
            
            setTimeout(() => {
                error.remove();
            }, 5000);
        }

        // Initialize when page loads
        window.addEventListener('load', initCamera);

        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                if (currentStream) {
                    currentStream.getTracks().forEach(track => track.stop());
                }
            } else {
                initCamera();
            }
        });
    </script>
</body>
</html>

// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite" // of "postgresql" voor productie
  url      = env("DATABASE_URL")
}

model Klant {
  id        String   @id @default(cuid())
  naam      String
  email     String?
  telefoon  String?
  adres     String?
  postcode  String?
  plaats    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  projecten Project[]
  facturen  Factuur[]
}

model Project {
  id              String   @id @default(cuid())
  naam            String
  beschrijving    String?
  status          ProjectStatus @default(AKTIEF)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Relaties
  klantId         String
  klant           Klant    @relation(fields: [klantId], references: [id])
  
  // Afmetingen
  lengte          Float?
  breedte         Float?
  hoogte          Float?
  oppervlakte     Float?   // Berekend veld
  
  // AI Analyse
  aiAnalyse       AIAnalyse?
  geschattePrijs  Float?
  
  // Media
  fotos           ProjectFoto[]
  
  // Administratie
  facturen        Factuur[]
  taken           Taak[]
}

model ProjectFoto {
  id          String   @id @default(cuid())
  filename    String
  url         String
  description String?
  aiAnalyse   String?  // JSON string van AI resultaat
  createdAt   DateTime @default(now())
  
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
}

model AIAnalyse {
  id                String   @id @default(cuid())
  materiaalType     String?
  conditieScore     Int?     // 1-10
  complexiteitscore Int?     // 1-10
  oppervlakteType   String?  // "glad", "ruw", "beschadigd"
  aanbevolenMethode String?
  geschatteTijd     Float?   // in uren
  geschatteKosten   Float?
  confidence        Float?   // 0-1
  rawData           String?  // JSON van complete AI response
  createdAt         DateTime @default(now())
  
  projectId         String   @unique
  project           Project  @relation(fields: [projectId], references: [id], onDelete: Cascade)
}

model Factuur {
  id          String        @id @default(cuid())
  nummer      String        @unique
  datum       DateTime      @default(now())
  vervaldatum DateTime?
  subtotaal   Float
  btw         Float
  totaal      Float
  status      FactuurStatus @default(CONCEPT)
  opmerkingen String?
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  
  // Relaties
  klantId     String
  klant       Klant         @relation(fields: [klantId], references: [id])
  projectId   String?
  project     Project?      @relation(fields: [projectId], references: [id])
  
  // Factuurregels
  regels      FactuurRegel[]
}

model FactuurRegel {
  id          String  @id @default(cuid())
  beschrijving String
  hoeveelheid Float
  eenheidsprijs Float
  totaal      Float
  
  factuurId   String
  factuur     Factuur @relation(fields: [factuurId], references: [id], onDelete: Cascade)
}

model Taak {
  id          String     @id @default(cuid())
  titel       String
  beschrijving String?
  status      TaakStatus @default(TODO)
  prioriteit  Prioriteit @default(NORMAAL)
  deadline    DateTime?
  createdAt   DateTime   @default(now())
  updatedAt   DateTime   @updatedAt
  
  projectId   String?
  project     Project?   @relation(fields: [projectId], references: [id])
}

// Enums
enum ProjectStatus {
  AKTIEF
  AFGEROND
  GEANNULEERD
  WACHT_OP_KLANT
}

enum FactuurStatus {
  CONCEPT
  VERZONDEN
  BETAALD
  ACHTERSTALLIG
}

enum TaakStatus {
  TODO
  BEZIG
  GEREED
}

enum Prioriteit {
  LAAG
  NORMAAL
  HOOG
  URGENT
}
{"name": "vloerwerk-crm", "version": "1.0.0", "description": "Vloerwerk CRM met AI-analyse en mobile PWA", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:studio": "prisma studio", "db:migrate": "prisma migrate dev", "type-check": "tsc --noEmit", "build-pwa": "next build && next export"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.2.0", "@types/node": "^20.8.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@prisma/client": "^5.5.0", "prisma": "^5.5.0", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "tailwindcss": "^3.3.5", "@tailwindcss/forms": "^0.5.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "lucide-react": "^0.290.0", "clsx": "^2.0.0", "tailwind-merge": "^1.14.0", "class-variance-authority": "^0.7.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "axios": "^1.5.1", "swr": "^2.2.4", "react-camera-pro": "^1.4.0", "react-image-crop": "^11.0.5", "html2canvas": "^1.4.1", "@tensorflow/tfjs": "^4.12.0", "@tensorflow/tfjs-node": "^4.12.0", "date-fns": "^2.30.0", "recharts": "^2.8.0", "jspdf": "^2.5.1", "html2pdf.js": "^0.10.1", "next-pwa": "^5.6.0", "workbox-webpack-plugin": "^7.0.0"}, "devDependencies": {"eslint": "^8.51.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.8.0", "@typescript-eslint/parser": "^6.8.0", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.5.6"}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}
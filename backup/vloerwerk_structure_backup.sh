vloerwerk-crm/
├── README.md
├── package.json
├── next.config.js
├── tailwind.config.js
├── tsconfig.json
├── prisma/
│   ├── schema.prisma
│   └── migrations/
├── public/
│   ├── manifest.json
│   ├── sw.js
│   ├── icons/
│   └── images/
├── src/
│   ├── app/
│   │   ├── layout.tsx
│   │   ├── page.tsx
│   │   ├── globals.css
│   │   ├── mobile/
│   │   │   ├── page.tsx
│   │   │   ├── project/
│   │   │   │   ├── new/page.tsx
│   │   │   │   └── [id]/page.tsx
│   │   │   └── components/
│   │   │       ├── CameraCapture.tsx
│   │   │       ├── ProjectForm.tsx
│   │   │       └── MeasurementInput.tsx
│   │   ├── desktop/
│   │   │   ├── page.tsx
│   │   │   ├── crm/
│   │   │   │   ├── dashboard/page.tsx
│   │   │   │   ├── klanten/page.tsx
│   │   │   │   ├── projecten/page.tsx
│   │   │   │   ├── facturen/page.tsx
│   │   │   │   └── rapporten/page.tsx
│   │   │   └── components/
│   │   │       ├── Sidebar.tsx
│   │   │       ├── ProjectTable.tsx
│   │   │       ├── InvoiceGenerator.tsx
│   │   │       └── AIAnalysisDisplay.tsx
│   │   └── api/
│   │       ├── klanten/route.ts
│   │       ├── projecten/route.ts
│   │       ├── upload/route.ts
│   │       ├── ai-analyze/route.ts
│   │       └── facturen/route.ts
│   ├── components/
│   │   ├── ui/
│   │   ├── shared/
│   │   └── forms/
│   ├── lib/
│   │   ├── db.ts
│   │   ├── ai-service.ts
│   │   ├── utils.ts
│   │   └── validations.ts
│   ├── types/
│   │   └── index.ts
│   └── hooks/
│       ├── useCamera.ts
│       ├── useProject.ts
│       └── useAIAnalysis.ts
└── docs/
    ├── database-schema.md
    ├── api-endpoints.md
    └── deployment.md
// src/lib/ai-service.ts
import { AIAnalyseResult, MateriaalType, OppervlakteType, AfmetingenInput, PrijsCalculatie } from '@/types';

interface VisionAnalysisResult {
  materiaalType: MateriaalType;
  conditieScore: number;
  complexiteitscore: number;
  oppervlakteType: OppervlakteType;
  confidence: number;
  details: {
    kleurToon: string;
    textuur: string;
    beschadigingen: string[];
    oppervlakteKwaliteit: number;
  };
}

interface PrijsberekeningParams {
  analysisResult: VisionAnalysisResult;
  afmetingen: AfmetingenInput;
  materiaalType: MateriaalType;
  projectType: 'nieuw' | 'renovatie' | 'reparatie';
}

class AIAnalysisService {
  private readonly API_BASE = '/api/ai-analyze';
  
  // Prijzen per m² per materiaal type (basis tarieven)
  private readonly MATERIAAL_PRIJZEN = {
    [MateriaalType.LAMINAAT]: { materiaal: 15, arbeid: 8 },
    [MateriaalType.PARKET]: { materiaal: 35, arbeid: 12 },
    [MateriaalType.TEGELS]: { materiaal: 25, arbeid: 15 },
    [MateriaalType.VINYL]: { materiaal: 20, arbeid: 6 },
    [MateriaalType.NATUURSTEEN]: { materiaal: 60, arbeid: 20 }
  };

  /**
   * Analyseer foto's van vloer met AI vision
   */
  async analyzeFloorImages(
    imageFiles: File[], 
    afmetingen: AfmetingenInput
  ): Promise<AIAnalyseResult> {
    try {
      // Upload images eerst
      const uploadedUrls = await this.uploadImages(imageFiles);
      
      // Stuur naar AI vision service
      const analysisResults = await Promise.all(
        uploadedUrls.map(url => this.analyzeImage(url))
      );
      
      // Combineer resultaten
      const combinedAnalysis = this.combineAnalysisResults(analysisResults);
      
      // Bereken prijsschatting
      const prijsschatting = this.calculatePrice({
        analysisResult: combinedAnalysis,
        afmetingen,
        materiaalType: combinedAnalysis.materiaalType,
        projectType: 'nieuw' // Default, kan later configureerbaar worden
      });

      return {
        materiaalType: combinedAnalysis.materiaalType,
        conditieScore: combinedAnalysis.conditieScore,
        complexiteitscore: combinedAnalysis.complexiteitscore,
        oppervlakteType: combinedAnalysis.oppervlakteType,
        aanbevolenMethode: this.getRecommendedMethod(combinedAnalysis),
        geschatteTijd: this.calculateEstimatedTime(afmetingen, combinedAnalysis),
        geschatteKosten: prijsschatting.totaal,
        confidence: combinedAnalysis.confidence,
        rawData: {
          visionResults: analysisResults,
          prijsBreakdown: prijsschatting,
          afmetingen
        }
      };
    } catch (error) {
      console.error('AI Analysis failed:', error);
      throw new Error('Kon foto\'s niet analyseren. Probeer opnieuw.');
    }
  }

  /**
   * Upload images naar cloud storage
   */
  private async uploadImages(files: File[]): Promise<string[]> {
    const uploadPromises = files.map(async (file) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', 'floor-analysis');
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      });
      
      if (!response.ok) {
        throw new Error('Upload failed');
      }
      
      const { url } = await response.json();
      return url;
    });
    
    return Promise.all(uploadPromises);
  }

  /**
   * Analyseer individuele foto met AI vision
   */
  private async analyzeImage(imageUrl: string): Promise<VisionAnalysisResult> {
    const response = await fetch(`${this.API_BASE}/vision`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        imageUrl,
        analysisType: 'floor_assessment',
        language: 'nl'
      })
    });

    if (!response.ok) {
      throw new Error('Vision analysis failed');
    }

    const result = await response.json();
    return this.parseVisionResult(result);
  }

  /**
   * Parse AI vision API response
   */
  private parseVisionResult(apiResult: any): VisionAnalysisResult {
    // Simulatie van AI vision response parsing
    // In productie zou dit een echte AI service aanroepen
    return {
      materiaalType: this.detectMaterialType(apiResult),
      conditieScore: this.calculateConditionScore(apiResult),
      complexiteitscore: this.calculateComplexityScore(apiResult),
      oppervlakteType: this.detectSurfaceType(apiResult),
      confidence: apiResult.confidence || 0.85,
      details: {
        kleurToon: apiResult.detectedColors?.[0] || 'onbekend',
        textuur: apiResult.textureAnalysis?.primary || 'glad',
        beschadigingen: apiResult.damageDetection || [],
        oppervlakteKwaliteit: apiResult.surfaceQuality || 8
      }
    };
  }

  /**
   * Detecteer materiaal type uit AI response
   */
  private detectMaterialType(apiResult: any): MateriaalType {
    const detected = apiResult.materialType?.toLowerCase() || '';
    
    if (detected.includes('laminaat') || detected.includes('laminate')) {
      return MateriaalType.LAMINAAT;
    }
    if (detected.includes('parket') || detected.includes('hardwood')) {
      return MateriaalType.PARKET;
    }
    if (detected.includes('tegel') || detected.includes('tile')) {
      return MateriaalType.TEGELS;
    }
    if (detected.includes('vinyl') || detected.includes('pvc')) {
      return MateriaalType.VINYL;
    }
    if (detected.includes('steen') || detected.includes('stone')) {
      return MateriaalType.NATUURSTEEN;
    }
    
    // Default fallback
    return MateriaalType.LAMINAAT;
  }

  /**
   * Bereken conditie score (1-10)
   */
  private calculateConditionScore(apiResult: any): number {
    const damages = apiResult.damageDetection?.length || 0;
    const wear = apiResult.wearLevel || 0;
    
    let score = 10;
    score -= damages * 1.5; // Aftrek per beschadiging
    score -= wear * 0.1; // Aftrek voor slijtage
    
    return Math.max(1, Math.min(10, Math.round(score)));
  }

  /**
   * Bereken complexiteit score (1-10)
   */
  private calculateComplexityScore(apiResult: any): number {
    const obstacles = apiResult.obstacleCount || 0;
    const irregularShapes = apiResult.irregularAreas || 0;
    const accessDifficulty = apiResult.accessDifficulty || 0;
    
    let score = 3; // Base complexiteit
    score += obstacles * 0.5;
    score += irregularShapes * 1;
    score += accessDifficulty * 2;
    
    return Math.max(1, Math.min(10, Math.round(score)));
  }

  /**
   * Detecteer oppervlakte type
   */
  private detectSurfaceType(apiResult: any): OppervlakteType {
    const texture = apiResult.textureAnalysis?.primary?.toLowerCase() || '';
    
    if (texture.includes('rough') || texture.includes('ruw')) {
      return OppervlakteType.RUW;
    }
    if (texture.includes('gestructureerd') || texture.includes('structured')) {
      return OppervlakteType.GESTRUCTUREERD;
    }
    
    return OppervlakteType.GLAD;
  }

  /**
   * Combineer multiple analyse resultaten
   */
  private combineAnalysisResults(results: VisionAnalysisResult[]): VisionAnalysisResult {
    if (results.length === 0) {
      throw new Error('Geen analyse resultaten om te combineren');
    }
    
    if (results.length === 1) {
      return results[0];
    }
    
    // Gewogen gemiddelde op basis van confidence
    const totalConfidence = results.reduce((sum, r) => sum + r.confidence, 0);
    
    return {
      materiaalType: this.getMostCommonMaterial(results),
      conditieScore: this.calculateWeightedAverage(
        results.map(r => ({ value: r.conditieScore, weight: r.confidence }))
      ),
      complexiteitscore: Math.max(...results.map(r => r.complexiteitscore)),
      oppervlakteType: this.getMostCommonSurfaceType(results),
      confidence: totalConfidence / results.length,
      details: this.combineDetails(results)
    };
  }

  /**
   * Bereken prijsschatting
   */
  private calculatePrice(params: PrijsberekeningParams): PrijsCalculatie {
    const { analysisResult, afmetingen, materiaalType } = params;
    
    const oppervlakte = afmetingen.lengte * afmetingen.breedte;
    const basisPrijzen = this.MATERIAAL_PRIJZEN[materiaalType];
    
    // Basis kosten
    let materiaalKosten = oppervlakte * basisPrijzen.materiaal;
    let arbeidKosten = oppervlakte * basisPrijzen.arbeid;
    
    // Correctie factoren
    const conditieFactor = (11 - analysisResult.conditieScore) / 10; // Slechtere conditie = duurder
    const complexiteitFactor = analysisResult.complexiteitscore / 10;
    
    materiaalKosten *= (1 + conditieFactor * 0.2);
    arbeidKosten *= (1 + complexiteitFactor * 0.5);
    
    const complexiteitToeslag = arbeidKosten * complexiteitFactor;
    const subtotaal = materiaalKosten + arbeidKosten + complexiteitToeslag;
    const btw = subtotaal * 0.21; // 21% BTW
    const totaal = subtotaal + btw;
    
    return {
      materiaalKosten: Math.round(materiaalKosten),
      arbeidKosten: Math.round(arbeidKosten),
      complexiteitToeslag: Math.round(complexiteitToeslag),
      subtotaal: Math.round(subtotaal),
      btw: Math.round(btw),
      totaal: Math.round(totaal)
    };
  }

  /**
   * Helper methods
   */
  private getMostCommonMaterial(results: VisionAnalysisResult[]): MateriaalType {
    const counts = results.reduce((acc, r) => {
      acc[r.materiaalType] = (acc[r.materiaalType] || 0) + 1;
      return acc;
    }, {} as Record<MateriaalType, number>);
    
    return Object.entries(counts).reduce((a, b) => 
      counts[a[0] as MateriaalType] > counts[b[0] as MateriaalType] ? a : b
    )[0] as MateriaalType;
  }

  private getMostCommonSurfaceType(results: VisionAnalysisResult[]): OppervlakteType {
    const counts = results.reduce((acc, r) => {
      acc[r.oppervlakteType] = (acc[r.oppervlakteType] || 0) + 1;
      return acc;
    }, {} as Record<OppervlakteType, number>);
    
    return Object.entries(counts).reduce((a, b) => 
      counts[a[0] as OppervlakteType] > counts[b[0] as OppervlakteType] ? a : b
    )[0] as OppervlakteType;
  }

  private calculateWeightedAverage(values: { value: number; weight: number }[]): number {
    const totalWeight = values.reduce((sum, v) => sum + v.weight, 0);
    const weightedSum = values.reduce((sum, v) => sum + (v.value * v.weight), 0);
    return Math.round(weightedSum / totalWeight);
  }

  private combineDetails(results: VisionAnalysisResult[]) {
    return {
      kleurToon: results[0].details.kleurToon,
      textuur: results[0].details.textuur,
      beschadigingen: results.flatMap(r => r.details.beschadigingen),
      oppervlakteKwaliteit: this.calculateWeightedAverage(
        results.map(r => ({ value: r.details.oppervlakteKwaliteit, weight: r.confidence }))
      )
    };
  }

  private getRecommendedMethod(analysis: VisionAnalysisResult): string {
    const { materiaalType, conditieScore, complexiteitscore } = analysis;
    
    if (conditieScore <= 4) {
      return 'Volledige vervanging aanbevolen';
    }
    
    if (complexiteitscore >= 7) {
      return 'Professionele installatie vereist';
    }
    
    switch (materiaalType) {
      case MateriaalType.LAMINAAT:
        return 'Click-systeem installatie';
      case MateriaalType.PARKET:
        return 'Lijmen of spijkeren';
      case MateriaalType.TEGELS:
        return 'Traditionele tegelzetting';
      default:
        return 'Standaard installatie';
    }
  }

  private calculateEstimatedTime(
    afmetingen: AfmetingenInput, 
    analysis: VisionAnalysisResult
  ): number {
    const oppervlakte = afmetingen.lengte * afmetingen.breedte;
    const basisTijd = oppervlakte * 0.5; // 30 min per m² basis
    const complexiteitToeslag = basisTijd * (analysis.complexiteitscore / 10) * 0.3;
    
    return Math.round(basisTijd + complexiteitToeslag);
  }
}

export const aiService = new AIAnalysisService();
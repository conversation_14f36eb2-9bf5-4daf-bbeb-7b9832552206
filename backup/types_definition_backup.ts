// src/types/index.ts

export interface KlantGegevens {
  id?: string;
  naam: string;
  email?: string;
  telefoon?: string;
  adres?: string;
  postcode?: string;
  plaats?: string;
}

export interface VloerProject {
  id?: string;
  naam: string;
  beschrijving?: string;
  klantId: string;
  klant?: KlantGegevens;
  status: ProjectStatus;
  
  // Afmetingen
  lengte?: number;
  breedte?: number;
  hoogte?: number;
  oppervlakte?: number;
  
  // AI Data
  aiAnalyse?: AIAnalyseResult;
  geschattePrijs?: number;
  
  // Media
  fotos: ProjectFoto[];
  
  createdAt?: Date;
  updatedAt?: Date;
}

export interface ProjectFoto {
  id?: string;
  filename: string;
  url: string;
  description?: string;
  aiAnalyse?: string; // JSON string
  createdAt?: Date;
}

export interface AIAnalyseResult {
  id?: string;
  materiaalType?: MaterialType;
  conditieScore?: number; // 1-10
  complexiteitscore?: number; // 1-10
  oppervlakteType?: OppervlakteType;
  aanbevolenMethode?: string;
  geschatteTijd?: number; // uren
  geschatteKosten?: number;
  confidence?: number; // 0-1
  rawData?: any;
}

export interface AfmetingenInput {
  lengte: number;
  breedte: number;
  hoogte?: number;
}

export interface FotoAnalyseInput {
  file: File;
  description?: string;
  afmetingen?: AfmetingenInput;
}

export interface AIAnalyseRequest {
  projectId: string;
  fotos: string[]; // URLs
  afmetingen: AfmetingenInput;
  projectType: 'vloerwerk' | 'tegelwerk' | 'parket';
}

export interface PrijsCalculatie {
  materiaalKosten: number;
  arbeidsKosten: number;
  gereedschapKosten: number;
  overheadPercentage: number;
  winstmarge: number;
  subtotaal: number;
  btw: number;
  totaal: number;
  breakdown: PrijsBreakdown[];
}

export interface PrijsBreakdown {
  categorie: string;
  beschrijving: string;
  hoeveelheid: number;
  eenheidsprijs: number;
  totaal: number;
}

// Enums
export enum ProjectStatus {
  AKTIEF = 'AKTIEF',
  AFGEROND = 'AFGEROND',
  GEANNULEERD = 'GEANNULEERD',
  WACHT_OP_KLANT = 'WACHT_OP_KLANT'
}

export enum MaterialType {
  LAMINAAT = 'laminaat',
  PARKET = 'parket',
  TEGELS = 'tegels',
  VINYL = 'vinyl',
  NATUURSTEEN = 'natuursteen'
}

export enum OppervlakteType {
  GLAD = 'glad',
  ROUW = 'ruw',
  BESCHADIGD = 'beschadigd',
  ONEVEN = 'oneven'
}

// Form Types voor components
export interface ProjectFormData {
  klant: KlantGegevens;
  projectNaam: string;
  beschrijving?: string;
  afmetingen: AfmetingenInput;
}

export interface CameraConfig {
  quality: number; // 0-1
  maxWidth: number;
  maxHeight: number;
  format: 'jpeg' | 'png' | 'webp';
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// Device Detection
export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  screenWidth: number;
  userAgent: string;
}

// PWA Types
export interface PWAInstallPrompt {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

// Local Storage Types
export interface ProjectDraft {
  id: string;
  formData: Partial<ProjectFormData>;
  fotos: File[];
  timestamp: number;
  autoSaved: boolean;
}
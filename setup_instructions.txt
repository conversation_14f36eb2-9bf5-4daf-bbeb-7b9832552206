# 🏗️ Vloerwerk CRM - Complete Setup Guide

Een moderne webapp met mobile PWA voor veldwerk en desktop CRM voor administratie.

## 🚀 Quick Start in Cursor

### 1. Project Initialisatie
```bash
# Maak nieuwe Next.js project
npx create-next-app@latest vloerwerk-crm --typescript --tailwind --eslint --app --src-dir
cd vloerwerk-crm

# Installeer dependencies
npm install @prisma/client prisma @radix-ui/react-dialog @radix-ui/react-dropdown-menu @radix-ui/react-select @radix-ui/react-tabs @radix-ui/react-toast @tailwindcss/forms lucide-react clsx tailwind-merge class-variance-authority react-hook-form @hookform/resolvers zod axios swr react-camera-pro react-image-crop html2canvas @tensorflow/tfjs @tensorflow/tfjs-node date-fns recharts jspdf html2pdf.js next-pwa workbox-webpack-plugin
```

### 2. Database Setup
```bash
# Initialiseer Prisma
npx prisma init

# Kopieer schema.prisma inhoud (zie artifact)
# Voer migratie uit
npx prisma migrate dev --name init
npx prisma generate
```

### 3. Environment Setup
```bash
# .env.local
DATABASE_URL="file:./dev.db"
NEXTAUTH_SECRET="your-secret-key"
AI_API_KEY="your-ai-api-key"
UPLOAD_DIR="./public/uploads"
```

### 4. Folder Structure
Kopieer de complete folder structure uit artifact "vloerwerk_structure"

### 5. Core Files Setup
1. **Database Schema**: Kopieer `prisma/schema.prisma`
2. **Types**: Kopieer `src/types/index.ts`
3. **Components**: Kopieer mobile en desktop components
4. **API Routes**: Kopieer alle API endpoints
5. **Services**: Kopieer AI service en utilities

## 📱 Mobile PWA Features

### Camera Integration
- Native camera access met fallback naar file upload
- Real-time foto preview en bewerking
- Automatische compressie en optimalisatie
- Offline functionaliteit met local storage

### Veldwerk Workflow
1. **Project Setup**: Klantgegevens + project naam invoeren
2. **Foto's Maken**: Multiple foto's met beschrijving
3. **Afmetingen**: Lengte, breedte, hoogte input
4. **AI Analyse**: Automatische materiaal detectie en prijsberekening
5. **Sync**: Upload naar server wanneer online

### Responsive Design
```typescript
// Device detection
const isMobile = typeof window !== 'undefined' && window.innerWidth < 768;

// Automatische redirect
useEffect(() => {
  if (isMobile && window.location.pathname === '/') {
    window.location.href = '/mobile';
  } else if (!isMobile && window.location.pathname === '/') {
    window.location.href = '/desktop';
  }
}, []);
```

## 🖥️ Desktop CRM Features

### Dashboard
- Real-time statistieken en KPI's
- Interactieve charts (Recharts)
- Recente projecten overzicht
- Quick actions voor veelgebruikte taken

### Project Management
- Volledige CRUD voor projecten
- AI analyse resultaten weergave
- Status tracking en workflow
- Foto galerij met zoom functionaliteit

### Facturatie Systeem
```typescript
interface FactuurGenerator {
  createFromProject(projectId: string): Factuur;
  generatePDF(factuurId: string): Buffer;
  sendEmail(factuurId: string, emailTemplate: string): Promise<void>;
  trackPayments(): void;
}
```

### CRM Functionaliteit
- Klanten database
- Project geschiedenis
- Communicatie logs
- Rapporten en analytics

## 🤖 AI Integration Strategy

### Vision Analysis Pipeline
```mermaid
graph LR
    A[Foto Upload] --> B[Preprocessing]
    B --> C[AI Vision API]
    C --> D[Materiaal Detectie]
    D --> E[Conditie Assessment]
    E --> F[Prijsberekening]
    F --> G[Database Storage]
```

### Lokale vs Cloud AI
**Optie A: Cloud-based (Aanbevolen voor MVP)**
- Google Vision API of Azure Computer Vision
- Snelle implementatie
- Hoge accuracy
- Per-request kosten

**Optie B: Lokale AI (Future Enhancement)**
- TensorFlow.js models
- Privacy-friendly
- Offline capability
- Hogere initial setup

### AI Service Implementation
```typescript
// Cloud AI service
const cloudAI = new CloudVisionService({
  provider: 'google', // of 'azure', 'aws'
  apiKey: process.env.AI_API_KEY,
  models: ['material-detection', 'damage-assessment']
});

// Lokale AI service  
const localAI = new LocalVisionService({
  modelPath: './models/floor-analysis.json',
  confidence: 0.8
});
```

## 🔧 Development Workflow

### 1. Cursor Setup
```json
// .cursor/settings.json
{
  "typescript.preferences.includePackageJsonAutoImports": "auto",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  }
}
```

### 2. Development Scripts
```bash
# Start development
npm run dev

# Database management
npm run db:studio    # Prisma Studio
npm run db:push      # Push schema changes
npm run db:migrate   # Create migration

# Type checking
npm run type-check

# Build PWA
npm run build-pwa
```

### 3. Code Generation Tips
- Gebruik Cursor's AI voor component boilerplate
- Auto-generate Prisma types: `npx prisma generate`
- Form validation met Zod schemas
- API routes met proper TypeScript typing

## 📊 Database Schema Highlights

### Relaties
```mermaid
erDiagram
    Klant ||--o{ Project : "heeft"
    Project ||--o{ ProjectFoto : "bevat"
    Project ||--o| AIAnalyse : "heeft"
    Project ||--o{ Factuur : "genereert"
    Project ||--o{ Taak : "heeft"
    Factuur ||--o{ FactuurRegel : "bevat"
```

### Key Tables
- **Klant**: Contact informatie en project historie
- **Project**: Kern entiteit met afmetingen en status
- **AIAnalyse**: AI resultaten en prijsberekeningen
- **ProjectFoto**: Media files met metadata
- **Factuur**: Financiële administratie

## 🔒 Security & Performance

### Beveiliging
```typescript
// Input validation
const projectSchema = z.object({
  naam: z.string().min(1).max(100),
  klantId: z.string().uuid(),
  lengte: z.number().positive().max(1000),
  breedte: z.number().positive().max(1000)
});

// File upload security
const allowedMimeTypes = ['image/jpeg', 'image/png', 'image/webp'];
const maxFileSize = 10 * 1024 * 1024; // 10MB
```

### Performance Optimalisatie
- Image compression met html2canvas
- Lazy loading voor foto galerijen
- SWR voor data caching
- Progressive image loading
- Database indexing op veelgebruikte queries

## 🚀 Deployment Opties

### Vercel (Aanbevolen)
```bash
# Deploy naar Vercel
npx vercel --prod

# Environment variables instellen
vercel env add DATABASE_URL
vercel env add AI_API_KEY
```

### Lokale Server
```bash
# Build productie versie
npm run build
npm start

# Of met PM2
npm install -g pm2
pm2 start ecosystem.config.js
```

### Docker Setup
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🔧 Customization Tips

### Branding
```css
/* globals.css - Pas kleuren aan */
:root {
  --primary: #3B82F6;
  --secondary: #10B981;
  --accent: #F59E0B;
  --background: #F9FAFB;
}
```

### AI Model Tuning
```typescript
// Pas prijzen aan in ai-service.ts
private readonly MATERIAAL_PRIJZEN = {
  [MaterialType.LAMINAAT]: { materiaal: 15, arbeid: 8 },
  [MaterialType.PARKET]: { materiaal: 35, arbeid: 12 },
  // Aangepast aan jouw prijsstructuur
};
```

### Workflow Aanpassingen
- Custom project statussen in database schema
- Approval workflows voor hogere bedragen
- Integration met externe systemen (boekhoudpakket)
- Automatische klant notificaties

## 📞 Next Steps

1. **MVP Launch**: Basic functionaliteit werkend krijgen
2. **AI Training**: Model fine-tunen met jouw project data
3. **Integrations**: Boekhoudpakket, email marketing
4. **Mobile App**: Native app met React Native
5. **Telegram Bot**: Notificaties en quick updates

## 🆘 Troubleshooting

### Common Issues
```bash
# Prisma schema out of sync
npx prisma db push
npx prisma generate

# Image upload fails
# Check upload directory permissions
chmod 755 public/uploads

# AI analysis timeouts
# Increase API timeout in next.config.js
module.exports = {
  api: {
    responseLimit: '8mb',
    bodyParser: {
      sizeLimit: '8mb',
    },
  },
}
```

Leon, deze complete setup geeft je alles wat je nodig hebt om te starten in Cursor! Welk onderdeel wil je eerst implementeren? 🚀
# Vloerwerk CRM - Project Rules & Development Guidelines

## 🏗️ PROJECT OVERZICHT

### **Vloerwerk CRM - AI-Powered Mobile-First CRM**
Een professionele CRM applicatie specifiek ontwikkeld voor vloerwerk bedrijven, met focus op mobile-first development voor veldwerkers.

### **🎯 Kernfunctionaliteiten:**
- **📸 Camera Capture:** Foto's maken van v<PERSON> met smartphone
- **🤖 AI Analyse:** Automatische materiaal detectie en prijsberekening  
- **📱 Mobile PWA:** Touch-friendly interface voor veldwerkers
- **🔄 Offline Werken:** Synchronisatie bij verbinding
- **📊 Project Management:** Project tracking en administratie

## 📱 MOBILE-FIRST DEVELOPMENT RULES

### **1. Mobile-First Prioriteit**
- **Eerst smartphone ontwikkelen** - Desktop komt later
- **Touch-friendly interface** - Grote knoppen, swipe gestures
- **Responsive design** - Werkt op alle mobile devices
- **PWA functionaliteit** - Installable, offline capable

### **2. Project Structuur**
```
src/
├── app/
│   ├── mobile/              # Mobile routes (prioriteit)
│   ├── api/                 # API routes
│   └── page.tsx            # Mobile-first landing
├── components/
│   └── mobile/             # Mobile components
├── lib/
│   ├── mobile/             # Mobile services
│   ├── api/                # API client
│   └── auth/               # Authentication
├── hooks/                  # Custom React hooks
└── types/
    └── mobile.ts           # Mobile-specific types
```

### **3. Mobile Features (Prioriteit)**
- **Camera Integration:** Foto capture, compressie, metadata
- **Offline Storage:** Local data, sync bij verbinding
- **GPS Location:** Project locatie tracking
- **Push Notifications:** Project updates
- **PWA Installation:** App installatie op device

### **4. Database Schema**
```typescript
// Core Entities
Project: { id, name, clientName, status, location, photos, analysis }
Photo: { id, url, filename, projectId, metadata, gps }
Analysis: { id, material, condition, priceEstimate, confidence }
User: { id, email, role, permissions }
```

### **5. AI Integration**
- **TensorFlow.js:** Client-side AI processing
- **Photo Analysis:** Materiaal detectie, conditie beoordeling
- **Price Calculation:** Automatische prijsberekening
- **Confidence Scoring:** AI betrouwbaarheid

## 🎨 UI/UX RULES

### **Mobile Interface Guidelines:**
- **Bottom Navigation:** Home, Camera, Projects, Settings
- **Large Touch Targets:** Minimaal 44px buttons
- **Swipe Gestures:** Intuitive navigation
- **Loading States:** Skeleton screens, spinners
- **Error Handling:** User-friendly error messages
- **Offline Indicators:** Duidelijke offline status

### **Color Scheme:**
- **Primary:** Blue (#3B82F6) - Trust, professional
- **Success:** Green (#10B981) - Completed projects
- **Warning:** Yellow (#F59E0B) - Pending actions
- **Error:** Red (#EF4444) - Errors, alerts
- **Background:** Gray (#F9FAFB) - Clean, minimal

## 🔧 TECHNICAL RULES

### **1. Code Organization:**
- **Types:** Definieer alle types in `/types/index.ts`
- **Services:** Singleton pattern voor services
- **Hooks:** Custom hooks voor reusable logic
- **Components:** Atomic design principles

### **2. API Structure:**
```typescript
// API Client
api.projects.getAll()
api.projects.create(data)
api.photos.upload(file)
api.ai.analyze(photoId)
api.auth.login(credentials)
```

### **3. State Management:**
- **React Hooks:** useState, useEffect, useContext
- **Local Storage:** Offline data persistence
- **Sync Service:** Background synchronization
- **Error Boundaries:** Graceful error handling

### **4. Performance Rules:**
- **Lazy Loading:** Component en route lazy loading
- **Image Optimization:** WebP format, compression
- **Bundle Splitting:** Code splitting per route
- **Caching:** Service worker caching strategy

## 🚀 DEVELOPMENT WORKFLOW

### **1. Mobile-First Development:**
1. **Start met mobile interface** (`/mobile`)
2. **Implementeer core features:** Camera, Projects, AI
3. **Test op mobile devices** - Real device testing
4. **Desktop interface later** - Na mobile completion

### **2. Feature Prioriteit:**
1. **Camera & Photo Capture** - Core functionality
2. **Project Management** - CRUD operations
3. **AI Analysis** - Photo processing
4. **Offline Support** - Data synchronization
5. **PWA Features** - Installation, notifications

### **3. Testing Strategy:**
- **Mobile Testing:** Chrome DevTools mobile simulation
- **Real Device Testing:** iOS/Android testing
- **Offline Testing:** Network throttling
- **Performance Testing:** Lighthouse audits

## 📋 BUSINESS RULES

### **1. User Roles:**
- **Admin:** Volledige toegang, gebruikersbeheer
- **ProjectManager:** Project management, AI features
- **Employee:** Toegewezen projecten, status updates
- **DemoUser:** Alleen lezen, geen AI

### **2. Pricing Logic:**
- **Materiaal detectie:** AI-based materiaal identificatie
- **Complexiteit factor:** Project moeilijkheidsgraad
- **BTW berekening:** Automatische BTW toevoeging
- **Tijdschatting:** AI-based tijd berekening

### **3. Data Privacy:**
- **GDPR Compliance:** Europese privacy wetgeving
- **Data Encryption:** End-to-end encryption
- **User Consent:** Explicit permission for camera/GPS
- **Data Retention:** Configurable retention policies

## 🔄 DEPLOYMENT RULES

### **1. Mobile PWA:**
- **Service Worker:** Offline functionality
- **Manifest:** App installation
- **HTTPS Required:** Security for PWA features
- **App Store:** Later iOS/Android deployment

### **2. Backend Services:**
- **API Routes:** Next.js API routes
- **Database:** Prisma ORM met SQLite/PostgreSQL
- **File Storage:** Local uploads, later cloud storage
- **AI Services:** TensorFlow.js client-side, later cloud AI

## 📝 DOCUMENTATION RULES

### **1. Code Comments:**
- **Dutch Comments:** Alle comments in Nederlands
- **Function Documentation:** JSDoc voor alle functies
- **Component Props:** TypeScript interfaces
- **API Documentation:** OpenAPI/Swagger specs

### **2. User Documentation:**
- **Mobile Guide:** Step-by-step mobile usage
- **Feature Videos:** Screen recordings van features
- **FAQ:** Veelgestelde vragen
- **Troubleshooting:** Common issues en solutions

---

## 🎯 SUCCESS METRICS

### **Mobile Performance:**
- **Load Time:** < 3 seconden op 3G
- **Offline Functionality:** 100% core features offline
- **Camera Performance:** < 2 seconden foto capture
- **AI Analysis:** < 5 seconden analyse tijd

### **User Experience:**
- **Intuitive Navigation:** < 3 clicks naar core features
- **Error Recovery:** Graceful error handling
- **Data Sync:** Seamless online/offline transition
- **PWA Installation:** < 10% abandonment rate

---

*Deze rules zijn het fundament voor mobile-first Vloerwerk CRM development. Alle development beslissingen moeten deze guidelines volgen.*

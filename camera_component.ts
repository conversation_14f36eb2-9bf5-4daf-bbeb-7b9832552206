// src/app/mobile/components/CameraCapture.tsx
'use client';

import { useState, useRef, useCallback } from 'react';
import { Camera, RotateCcw, Check, X } from 'lucide-react';
import { ProjectFoto, CameraConfig } from '@/types';

interface CameraCaptureProps {
  onPhotoCapture: (photo: File, description?: string) => void;
  maxPhotos?: number;
  currentPhotos?: ProjectFoto[];
}

export default function CameraCapture({ 
  onPhotoCapture, 
  maxPhotos = 10,
  currentPhotos = []
}: CameraCaptureProps) {
  const [isCapturing, setIsCapturing] = useState(false);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [description, setDescription] = useState('');
  const [stream, setStream] = useState<MediaStream | null>(null);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const cameraConfig: CameraConfig = {
    quality: 0.8,
    maxWidth: 1920,
    maxHeight: 1080,
    format: 'jpeg'
  };

  const startCamera = useCallback(async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Back camera
          width: { ideal: cameraConfig.maxWidth },
          height: { ideal: cameraConfig.maxHeight }
        }
      });
      
      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
      setIsCapturing(true);
    } catch (error) {
      console.error('Camera access error:', error);
      // Fallback naar file input
      fileInputRef.current?.click();
    }
  }, []);

  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setIsCapturing(false);
  }, [stream]);

  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');
    
    if (!context) return;

    // Set canvas dimensions
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    
    // Draw video frame to canvas
    context.drawImage(video, 0, 0);
    
    // Convert to data URL
    const dataUrl = canvas.toDataURL(`image/${cameraConfig.format}`, cameraConfig.quality);
    setCapturedImage(dataUrl);
    stopCamera();
  }, [stopCamera, cameraConfig]);

  const confirmPhoto = useCallback(async () => {
    if (!capturedImage) return;

    // Convert data URL to File
    const response = await fetch(capturedImage);
    const blob = await response.blob();
    const file = new File([blob], `vloer-${Date.now()}.${cameraConfig.format}`, {
      type: `image/${cameraConfig.format}`
    });

    onPhotoCapture(file, description);
    
    // Reset state
    setCapturedImage(null);
    setDescription('');
  }, [capturedImage, description, onPhotoCapture, cameraConfig.format]);

  const retakePhoto = useCallback(() => {
    setCapturedImage(null);
    setDescription('');
    startCamera();
  }, [startCamera]);

  const handleFileSelect = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onPhotoCapture(file, description);
      setDescription('');
    }
  }, [description, onPhotoCapture]);

  const isMaxPhotosReached = currentPhotos.length >= maxPhotos;

  return (
    <div className="w-full max-w-md mx-auto bg-white rounded-lg shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-blue-600 text-white p-4">
        <h3 className="text-lg font-semibold">Foto's maken</h3>
        <p className="text-sm opacity-90">
          {currentPhotos.length}/{maxPhotos} foto's
        </p>
      </div>

      {/* Camera View */}
      <div className="relative">
        {!capturedImage && !isCapturing && (
          <div className="aspect-video bg-gray-100 flex items-center justify-center">
            <button
              onClick={startCamera}
              disabled={isMaxPhotosReached}
              className="flex flex-col items-center gap-2 p-6 text-gray-600 disabled:opacity-50"
            >
              <Camera size={48} />
              <span className="text-sm">
                {isMaxPhotosReached ? 'Maximum bereikt' : 'Camera starten'}
              </span>
            </button>
          </div>
        )}

        {isCapturing && (
          <div className="relative aspect-video">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
            />
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
              <div className="flex gap-4">
                <button
                  onClick={stopCamera}
                  className="bg-red-500 text-white p-3 rounded-full shadow-lg"
                >
                  <X size={24} />
                </button>
                <button
                  onClick={capturePhoto}
                  className="bg-white text-gray-800 p-4 rounded-full shadow-lg border-4 border-gray-300"
                >
                  <div className="w-6 h-6 bg-gray-800 rounded-full" />
                </button>
              </div>
            </div>
          </div>
        )}

        {capturedImage && (
          <div className="relative aspect-video">
            <img
              src={capturedImage}
              alt="Captured"
              className="w-full h-full object-cover"
            />
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
              <div className="flex gap-4">
                <button
                  onClick={retakePhoto}
                  className="bg-gray-500 text-white p-3 rounded-full shadow-lg"
                >
                  <RotateCcw size={24} />
                </button>
                <button
                  onClick={confirmPhoto}
                  className="bg-green-500 text-white p-3 rounded-full shadow-lg"
                >
                  <Check size={24} />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Description Input */}
      {capturedImage && (
        <div className="p-4">
          <textarea
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="Beschrijving (optioneel)..."
            className="w-full p-3 border border-gray-300 rounded-lg resize-none"
            rows={2}
          />
        </div>
      )}

      {/* Fallback File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        capture="environment"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Alternative Upload Button */}
      {!isCapturing && !capturedImage && (
        <div className="p-4 border-t">
          <button
            onClick={() => fileInputRef.current?.click()}
            disabled={isMaxPhotosReached}
            className="w-full p-3 text-blue-600 border border-blue-600 rounded-lg disabled:opacity-50"
          >
            Of selecteer uit galerij
          </button>
        </div>
      )}

      {/* Hidden Canvas for Image Processing */}
      <canvas ref={canvasRef} className="hidden" />
    </div>
  );
}